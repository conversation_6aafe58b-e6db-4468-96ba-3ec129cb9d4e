import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 用于生成'5c'行insert语句的工具类
 * 基于LicTrialInstaller.java中的setDeployTime方法逻辑
 */
public class InsertGenerator {
    
    /**
     * AES加密密钥（来自LicTrialInstaller.java）
     */
    private static final String AES_KEY = "8D0/0GnGvIy8FFAc0g+vgw==";
    
    /**
     * 生成'5c'行的insert语句
     * @return 完整的insert语句字符串
     */
    public static String generateInsertStatement() {
        try {
            // 生成当前时间戳
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            String dateStr = simpleDateFormat.format(new Date());
            
            // AES加密时间戳
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] data = dateStr.getBytes();
            byte[] cipherByte = cipher.doFinal(data);
            String encryptedDateStr = Base64.getEncoder().encodeToString(cipherByte);
            
            // 生成insert语句（参照LicTrialInstaller.java第119行）
            String insertSql = "insert into gsppubextconfig(id,code,createdby,createdon,ext1,ext2,ext3,ext4,lastchangedby,lastchangedon) values('5c','pkp','client001',CURRENT_DATE,'" + encryptedDateStr + "','hfueiwhfewhfie','hfueiwhfewhfie','iewojfioewjfjj','client002',CURRENT_DATE)";
            
            return insertSql;
            
        } catch (Exception e) {
            throw new RuntimeException("生成insert语句失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 主方法，用于测试和输出insert语句
     */
    public static void main(String[] args) {
        try {
            String insertStatement = generateInsertStatement();
            System.out.println("生成的insert语句：");
            System.out.println(insertStatement);
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
