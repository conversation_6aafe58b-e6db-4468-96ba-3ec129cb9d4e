import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 用于生成'5c'行insert语句的工具类
 * 基于LicTrialInstaller.java中的setDeployTime方法逻辑
 */
public class InsertGenerator {
    
    /**
     * AES加密密钥（来自LicTrialInstaller.java）
     */
    private static final String AES_KEY = "8D0/0GnGvIy8FFAc0g+vgw==";
    
    /**
     * 生成'5c'行的insert语句
     * @return 完整的insert语句字符串
     */
    public static String generateInsertStatement() {
        try {
            // 生成当前时间戳
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            String dateStr = simpleDateFormat.format(new Date());
            
            // AES加密时间戳
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] data = dateStr.getBytes();
            byte[] cipherByte = cipher.doFinal(data);
            String encryptedDateStr = Base64.getEncoder().encodeToString(cipherByte);
            
            // 生成insert语句（参照LicTrialInstaller.java第119行）
            String insertSql = "insert into gsppubextconfig(id,code,createdby,createdon,ext1,ext2,ext3,ext4,lastchangedby,lastchangedon) values('5c','pkp','client001',CURRENT_DATE,'" + encryptedDateStr + "','hfueiwhfewhfie','hfueiwhfewhfie','iewojfioewjfjj','client002',CURRENT_DATE)";
            
            return insertSql;
            
        } catch (Exception e) {
            throw new RuntimeException("生成insert语句失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解密AES加密的字符串
     * @param encryptedStr 加密的字符串
     * @return 解密后的字符串
     */
    public static String decryptAES(String encryptedStr) {
        try {
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedStr));
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 主方法，用于测试和输出insert语句
     */
    public static void main(String[] args) {
        try {
            // 生成新的insert语句
            String insertStatement = generateInsertStatement();
            System.out.println("生成的insert语句：");
            System.out.println(insertStatement);

            System.out.println("\n=== 解密测试 ===");
            // 尝试解密数据库中的ext1
            String ext1FromDB = "fwdk+wlqQrmg2mp2yudVZVyByl0GRrXgtq2Upl9u31c=";
            try {
                String decryptedExt1 = decryptAES(ext1FromDB);
                System.out.println("数据库ext1解密结果: " + decryptedExt1);
            } catch (Exception e) {
                System.out.println("ext1解密失败: " + e.getMessage());
            }

            // 尝试解密数据库中的ext2
            String ext2FromDB = "VN0ijAr5+ckaycOgUHtD+XlM0esOM6mNWCInQKQqeewSacQ=";
            try {
                String decryptedExt2 = decryptAES(ext2FromDB);
                System.out.println("数据库ext2解密结果: " + decryptedExt2);
            } catch (Exception e) {
                System.out.println("ext2解密失败: " + e.getMessage());
            }

        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
