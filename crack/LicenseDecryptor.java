import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;

/**
 * Standalone Java program to decrypt license strings using RSA public key decryption
 * Based on the SecretUtil.strHanding method implementation
 */
public class LicenseDecryptor {
    
    /**
     * RSA public key for decryption (from gsppubextconfig table, ext3 field where key='pk')
     */
    private static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJtQeCRUgTSbyrpJsXl3ujw68BnUEBJwg+AXuJ6OyKP7y3EBgjVb569Cw7JGsL76vrSIvQgWwVpZ8CULQxzpyTc6PhkTp7VN/VTdcyurUqEHiHbaDHbd+R4KFduT3mrRQAyuiPTX29t8FBLzteySjKb8c5MnQcIpND/omVYfUZQwIDAQAB";
    
    /**
     * Signature for key verification (from the original code)
     */
    private static final String KEY_SIGNATURE = "MCwCFEuMf0SwVAlYkfV/kVm/n5Fya9YnAhQOAHH9VDKFi3zeqlWZk95Bzx3Ghg==";
    
    public static void main(String[] args) {
        // The encrypted string you provided
        String encryptedString = "B7Tlfbo6xR6Po1m1SildtBpsDfEFG3TpzCrvmbf1WvJ5mmTFajDBOcOtnAz4NjaJ9E/tE6oyWOlI4nnSl0hCd6Z8ArmNT+VJf5QC85ZUtYO+HKYj1w+ARnsY+ghnSEY+Bre5ZtgYtjo4fkQK06PsBnig02pSIt2xA9gnHDM2a7h0RFVG8TxwM/hB+NeTvRlASwrmDy+gAGcvUf40wHCNR3juDvITVHf5Z2RTGTNdOw4iQugYBTKT1EUyIfcvOq8AGWml2/6+MOASyVI6PAu7jVUS6WtTd7VVrYgPKUUxZr4je4/8xYwzNgmrWWfB0QVRvAs8e393mNW8ZZK6ry8E648KbKGCgFsjrY5+aTIGe4frWM24RXA3D6LzcV5YMBiqEjmeAnCDwEcgB8VIUF4PUnkUD3b8HLBSdDahO8n2CVypah/28r26CqLyWCkmsgsZpEXIMKWARmPY1RuHVy7bffZiUnSPS8/X+Kh2FzN/7yDphQItM5riiYj6/TddW01psswaZIEQAE6YlWs5D0URaptmULAuqdQilCLIUPRp7jdlnB+35KnpsmnzfbtvPYScNlcGrENPiymRDG0LLawfz/2CGlqLxvZxJQsj2KE5CNh8GMehFLfvoxzOa86uWV8Fja6xr9sK9O3OwiKD/6SVYoebc9WqwgCHslZdQznNsNekUvr48auYqbAY8PQi7vF9iuwrTBNDyJtcDK8d3wAOG8VacGM1TtQmEXdJwpKmTF05Z6F85qWWWY3tA6KhMfSow8WRfD2fyOplQ3Sdh9F28C6XmItAOouiAM5UYQxU4u0q6TGX84rqbcyexOxRn4iIVH+KN+f4vOMAO4us2vLEkyXDYGqe8OxUpoXVqeHfCVJO3xM9E5iS+eP2J8njz9yWhFwz+wusXBhNw356Annw609twnMhCHkCdhlUD5noI6OYLR26AbU/hkGRGG+PXQWCO4XD1HtB5sK/tGeLH2mKLyL7s0cdE6MmXcMv23a1xrH1S5QhqA+bz7ZVxMEzTDhXB33wLs75xsacy3qu+p1svzbeTLnvw3BlFSE/+fu5lkoHNUwDOKM/Jlzd1lUcQldq3AJvoeBKxTtLA0/gAJvV0NjKZl/2FlM1YFBOL7OVPopGT++bjLSN7NTcRpE+Q8Ju8XDCCMw2Db9XvTS8YEq5XfepWwB0L0kqoQ00oVbDKyI=";
        
        try {
            // Decrypt the string
            String decryptedContent = decryptLicenseString(encryptedString);
            System.out.println("Decrypted content:");
            System.out.println(decryptedContent);
        } catch (Exception e) {
            System.err.println("Decryption failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Decrypts the license string using RSA public key decryption
     * This method replicates the logic from SecretUtil.strHanding
     * 
     * @param encryptedContent The encrypted license string
     * @return The decrypted content
     * @throws Exception If decryption fails
     */
    public static String decryptLicenseString(String encryptedContent) throws Exception {
        // Note: In the original code, key verification is performed here
        // You may need to implement verifySign method if key verification is required
        
        // Key verification (simplified - original code uses DataSec.verifySign)
        System.out.println("Using RSA public key from gsppubextconfig table");
        
        // Perform RSA decryption with public key
        String decryptedText = rsaDecryptWithPublicKey(encryptedContent, PUBLIC_KEY);
        
        // Remove null characters (as done in original code)
        decryptedText = decryptedText.replaceAll("\u0000", "");
        
        return decryptedText;
    }
    
    /**
     * Performs RSA decryption using public key - exact replication of RSACryptoProvider logic
     * Based on RSACryptoProvider.decrypt with isReverse=true and doFinal implementation
     * 
     * @param encryptedData Base64 encoded encrypted data
     * @param publicKeyStr Base64 encoded public key
     * @return Decrypted string
     * @throws Exception If decryption fails
     */
    private static String rsaDecryptWithPublicKey(String encryptedData, String publicKeyStr) throws Exception {
        // Decode the public key
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);
        
        // Decode the encrypted data
        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
        
        // Use RSA/ECB/NoPadding as in RSACryptoProvider
        Cipher cipher = Cipher.getInstance("RSA/ECB/NoPadding");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        
        // Replicate the exact doFinal logic from RSACryptoProvider (mode == 2, decrypt)
        int fullBlockSize = 128;
        int blockSize = fullBlockSize - 1; // 127
        int blockNum = (encryptedBytes.length - 1) / fullBlockSize + 1;
        int resultSize = blockNum * blockSize;
        byte[] result = new byte[resultSize];
        
        System.out.println("Total encrypted data length: " + encryptedBytes.length);
        System.out.println("Block count: " + blockNum);
        
        for (int i = 0; i < blockNum; i++) {
            // Create 128-byte block for decryption
            byte[] blockNow = new byte[fullBlockSize];
            int copyLength = Math.min(fullBlockSize, encryptedBytes.length - i * fullBlockSize);
            System.arraycopy(encryptedBytes, i * fullBlockSize, blockNow, 0, copyLength);
            
            System.out.println("Decrypting block " + (i + 1) + " of " + blockNum + ", copy length: " + copyLength);
            
            // Decrypt the block
            byte[] resultNow = cipher.doFinal(blockNow);
            
            // CRITICAL: Remove first byte from decrypted result (as per line 124 in RSACryptoProvider)
            System.arraycopy(resultNow, 1, result, i * blockSize, resultNow.length - 1);
            
            System.out.println("Block " + (i + 1) + " decrypted successfully");
        }
        
        // Remove trailing zeros (as done in removeBottomZero method)
        int endPosition = result.length;
        for (int i = result.length; i > 0; i--) {
            endPosition = i;
            if (result[i - 1] != 0) {
                break;
            }
        }
        
        byte[] finalResult = new byte[endPosition];
        System.arraycopy(result, 0, finalResult, 0, endPosition);
        
        return new String(finalResult, "UTF-8");
    }
    
    /**
     * Optional: Key verification method (simplified version)
     * You would need to implement this based on your signature verification requirements
     */
    private static boolean verifyKeySignature(String publicKey, String signature) {
        // This is a placeholder - implement actual signature verification if needed
        // The original code uses DataSec.verifySign method
        return true;
    }
}