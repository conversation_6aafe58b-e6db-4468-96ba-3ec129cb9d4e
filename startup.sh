#!/bin/bash

# 设置标题（在 Linux 中不需要）
# 获取当前路径
SERVER_PATH=`dirname $0`

# 检查目录是否存在
if [ -d "$SERVER_PATH/deploy" ]; then
    # 执行 caf.sh
    "$SERVER_PATH/igix.sh" start
else
    os400=false
    case "`uname`" in
    OS400*) os400=true;;
    esac

    # resolve links - $0 may be a softlink
    PRG="$0"

    while [ -h "$PRG" ] ; do
      ls=`ls -ld "$PRG"`
      link=`expr "$ls" : '.*-> \(.*\)$'`
      if expr "$link" : '/.*' > /dev/null; then
        PRG="$link"
      else
        PRG=`dirname "$PRG"`/"$link"
      fi
    done

    PRGDIR=`dirname "$PRG"`
    EXECUTABLE=caf-server.sh

    # Check that target executable exists
    if $os400; then
      # -x will Only work on the os400 if the files are:
      # 1. owned by the user
      # 2. owned by the PRIMARY group of the user
      # this will not work if the user belongs in secondary groups
      eval
    else
      if [ ! -x "$PRGDIR"/"$EXECUTABLE" ]; then
        if [ ! -x "$PRGDIR"/bin/"$EXECUTABLE" ]; then
          echo "Cannot find $PRGDIR/$EXECUTABLE"
          echo "The file is absent or does not have execute permission"
          echo "This file is needed to run this program"
          exit 1
        else
          EXECUTABLE=bin/$EXECUTABLE
        fi
      fi
    fi

    exec "$PRGDIR"/"$EXECUTABLE" debug -remote "$@"
fi

