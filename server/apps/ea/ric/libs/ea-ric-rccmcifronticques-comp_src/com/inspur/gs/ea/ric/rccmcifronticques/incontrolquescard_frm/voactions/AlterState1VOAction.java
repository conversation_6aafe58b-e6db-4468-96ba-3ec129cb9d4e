package com.inspur.gs.ea.ric.rccmcifronticques.incontrolquescard_frm.voactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.api.lcp.LcpFactoryManagerUtils;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;

public class AlterState1VOAction extends AbstractFSAction<VoidActionResult> {
   private String ID;
   private IBqlExecuter bqlExecuter;

   public AlterState1VOAction(String ID) {
      this.ID = ID;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("569b828f-d70a-4c78-ba99-2e5ccc0d6d2d");
      IDbParameter[] iDbParameters = new IDbParameter[1];
      iDbParameters[0] = bqlExecuter.makeInParam("CL", this.ID);
      String bql = "update InControlQues.InControlQues  set Bstatus = '1' where ID = @CL";
      bqlExecuter.executeBqlStatement(bql, refEntityIDs, iDbParameters);
      IStandardLcp lcp = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.rccmciicques.InControlQues");
      ModifyChangeDetail change = new ModifyChangeDetail(this.ID);
      change.getPropertyChanges().put("Bstatus", "Submit");
      lcp.modify(change);
   }
}
