package com.inspur.gs.ea.ric.rccmcifronticques.incontrolqueslist_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.CafSession;
import java.util.ArrayList;
import org.apache.cxf.common.util.StringUtils;

public class DeptFilterVOAction extends BeforeQueryAction {
   public DeptFilterVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      CafSession webSession = CAFContext.current.getCurrentSession();
      String dept = webSession.getSysOrgId();
      if (!StringUtils.isEmpty(dept)) {
         EntityFilter filter = this.getQueryContext().getFilter();
         ArrayList<FilterCondition> list = new ArrayList();
         FilterCondition filterCondition = new FilterCondition(0, "DeptId", ExpressCompareType.Equal, dept, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         filter.addFilterConditions(list);
      }

   }
}
