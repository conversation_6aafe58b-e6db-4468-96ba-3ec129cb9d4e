package com.inspur.gs.ea.ric.rccmcifronticques.incontrolquescard_frm.voactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class AddRiskVOAction extends AbstractFSAction<VoidActionResult> {
   private String ID;
   private IBqlExecuter bqlExecuter;

   public AddRiskVOAction(String ID) {
      this.ID = ID;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("35b2027c-5524-409d-8a3f-1249196c13ac");
      IDbParameter[] Parameters = new IDbParameter[]{bqlExecuter.makeInParam("CL", this.ID)};
      String bql = "SELECT RISKCODE,RISKNAME,RISKGRADE FROM EARISKCHECKLIST WHERE EARISKCHECKLIST.ID  = @CL";
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);

      for(int i = 0; i < resultCheckOrgType.size(); ++i) {
         IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("RiskCode", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(0)), bqlExecuter.makeInParam("RiskName", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(1)), bqlExecuter.makeInParam("RiskGrade", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(2))};
         String InsertBql = "UPDATE EAINCONTROLMATRIX set RISKCODE = :RiskCode, RISKNAME = :RiskName, RISKGRADE = :RISKGRADEwhere  EAINCONTROLMATRIX.RISKID = @CL";
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs1 = new ArrayList();
         refEntityIDs1.add("4b210063-b3a5-4371-b351-c5ef1ae03d50");
         bqlExecuter.executeBqlStatement(InsertBql, refEntityIDs1, iDbParameters);
      }

   }
}
