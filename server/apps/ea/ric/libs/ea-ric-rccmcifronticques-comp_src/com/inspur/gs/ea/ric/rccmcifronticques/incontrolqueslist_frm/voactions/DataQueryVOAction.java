package com.inspur.gs.ea.ric.rccmcifronticques.incontrolqueslist_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.CafSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.cxf.common.util.StringUtils;

public class DataQueryVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;

   public DataQueryVOAction(QueryContext context) {
      super(context);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "RiskPlanDownType")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> result1 = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      String approveProcess = "";
      if (CollectionUtils.isNotEmpty(result1)) {
         approveProcess = ((DynamicResultRow)result1.get(0)).get("configvalue").toString();
      }

      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      CafSession webSession = CAFContext.current.getCurrentSession();
      String dept = webSession.getSysOrgId();
      String userid = webSession.getUserId();
      ArrayList<String> ListEntity = new ArrayList();
      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      IDbParameter IDbParameters = bqlExecuter.makeInParam("ID", userid);
      String bql = "select t.ORGANIZATIONID from GSPWFREPORTINGLINEDATA t where t.REPORTINGLINEDEFID=(select t.id from GSPWFREPORTINGLINEDEF t where t.code='RicPlanDown') and t.reportingtoid=:ID";
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> deptid = bqlExecuter.executeSelectStatement(bql, ListEntity, new IDbParameter[]{IDbParameters});
      String str = "";
      if (approveProcess.equals("ReportLine")) {
         if (deptid.size() > 0) {
            for(DynamicResultRow res : deptid) {
               str = str + res.getValues().get(0).toString() + "\r\n";
            }

            str = str.substring(0, str.length() - 2);
         }

         FilterCondition filterCondition = new FilterCondition(0, "DeptId", ExpressCompareType.In, str, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         filter.addFilterConditions(list);
      } else if (!StringUtils.isEmpty(dept)) {
         FilterCondition filterCondition = new FilterCondition(0, "DeptId", ExpressCompareType.Equal, dept, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         filter.addFilterConditions(list);
      }

   }
}
