package com.inspur.gs.ea.ric.ricclps.extend;

import com.inspur.fastdweb.model.excel.ExcelWhere;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import java.util.List;

public class CLPSExportExtend implements IExcelImportEvent {
   public void beforeExcelExport(List<List<Object>> dataList, List<List<String>> headList, ExcelWhere sqlWhere) {
      int indexA = -1;
      int indexB = -1;
      int indexC = -1;
      int indexD = -1;
      int indexE = -1;

      for(int i = 0; i < headList.size(); ++i) {
         for(String s : (List)headList.get(i)) {
            if ("人员类型".equals(s)) {
               indexA = i;
            } else if ("属性".equals(s)) {
               indexB = i;
            } else if ("是否兼职".equals(s)) {
               indexC = i;
            } else if ("是否由法律顾问兼任".equals(s)) {
               indexD = i;
            } else if ("状态".equals(s)) {
               indexE = i;
            }
         }
      }

      for(List<Object> list : dataList) {
         if (indexA != -1) {
            String newValue = list.get(indexA).toString().equals("1") ? "合规联络员" : "联络专员";
            list.set(indexA, newValue);
         }

         if (indexB != -1) {
            String newValue = list.get(indexB).toString().equals("1") ? "独立" : "合署";
            list.set(indexB, newValue);
         }

         if (indexC != -1) {
            String newValue = list.get(indexC).toString().equals("1") ? "是" : "否";
            list.set(indexC, newValue);
         }

         if (indexD != -1) {
            String newValue = list.get(indexD).toString().equals("1") ? "是" : "否";
            list.set(indexD, newValue);
         }

         if (indexE != -1) {
            String newValue = list.get(indexE).toString().equals("1") ? "启用" : "停用";
            list.set(indexE, newValue);
         }
      }

      super.beforeExcelExport(dataList, headList, sqlWhere);
   }
}
