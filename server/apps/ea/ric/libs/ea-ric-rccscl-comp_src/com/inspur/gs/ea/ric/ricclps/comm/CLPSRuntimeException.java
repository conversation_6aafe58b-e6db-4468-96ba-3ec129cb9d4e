package com.inspur.gs.ea.ric.ricclps.comm;

public class CLPSRuntimeException extends RuntimeException {
   private String moduleCode;
   private String id;
   private String code;

   public CLPSRuntimeException() {
      this.moduleCode = "RIC";
   }

   public CLPSRuntimeException(String message) {
      super(message);
      this.moduleCode = "RIC";
   }

   public CLPSRuntimeException(String message, Throwable cause) {
      super(message, cause);
      this.moduleCode = "RIC";
   }

   public CLPSRuntimeException(Throwable cause) {
      super(cause);
      this.moduleCode = "RIC";
   }

   public CLPSRuntimeException(String moduleCode, String message) {
      this(message);
      this.moduleCode = moduleCode;
   }

   public CLPSRuntimeException(String moduleCode, String message, Throwable cause) {
      this(message, cause);
      this.moduleCode = moduleCode;
   }

   public String getModuleCode() {
      return this.moduleCode;
   }

   public void setModuleCode(String moduleCode) {
      this.moduleCode = moduleCode;
   }

   public String getId() {
      return this.id;
   }

   public void setId(String id) {
      this.id = id;
   }

   public String getCode() {
      return this.code;
   }

   public void setCode(String code) {
      this.code = code;
   }
}
