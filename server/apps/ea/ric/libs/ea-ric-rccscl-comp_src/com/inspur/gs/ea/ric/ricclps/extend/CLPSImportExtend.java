package com.inspur.gs.ea.ric.ricclps.extend;

import com.inspur.fastdweb.model.excel.ExcelObject;
import com.inspur.fastdweb.model.excel.ExcelResult;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import com.inspur.gs.ea.ric.ricclps.comm.CLPSRuntimeException;
import io.iec.edp.caf.commons.utils.StringUtils;

public class CLPSImportExtend implements IExcelImportEvent {
   public ExcelObject beforeImport(ExcelObject excelObject) {
      StringBuilder errorMsg = new StringBuilder();

      for(int i = Integer.parseInt(excelObject.rowStart); i < excelObject.rows.size(); ++i) {
         ExcelResult excelResult = (ExcelResult)excelObject.rows.get(i);
         String units = (String)excelResult.result.get(3);
         if (!StringUtils.isEmpty(units) && units.getBytes().length > 200) {
            errorMsg.append("第").append(i).append("行单位过长\n");
         }

         String age = (String)excelResult.result.get(7);
         if (!StringUtils.isEmpty(age) && age.getBytes().length > 36) {
            errorMsg.append("第").append(i).append("行年龄过长\n");
         }

         String phone = (String)excelResult.result.get(9);
         if (!StringUtils.isEmpty(phone) && phone.getBytes().length > 36) {
            errorMsg.append("第").append(i).append("行联系电话过长\n");
         }

         String email = (String)excelResult.result.get(10);
         if (!StringUtils.isEmpty(email) && email.getBytes().length > 36) {
            errorMsg.append("第").append(i).append("行邮箱过长\n");
         }

         String education = (String)excelResult.result.get(11);
         if (!StringUtils.isEmpty(education) && education.getBytes().length > 100) {
            errorMsg.append("第").append(i).append("行学历过长\n");
         }

         String majorName = (String)excelResult.result.get(12);
         if (!StringUtils.isEmpty(majorName) && majorName.getBytes().length > 100) {
            errorMsg.append("第").append(i).append("行专业名称过长\n");
         }

         String yearsWorking = (String)excelResult.result.get(13);
         if (!StringUtils.isEmpty(yearsWorking) && yearsWorking.getBytes().length > 100) {
            errorMsg.append("第").append(i).append("行从事本岗年限过长\n");
         }

         String HGPOSTDUTY = (String)excelResult.result.get(15);
         if (!StringUtils.isEmpty(HGPOSTDUTY) && HGPOSTDUTY.getBytes().length > 2000) {
            errorMsg.append("第").append(i).append("行合规岗位职责过长\n");
         }
      }

      if (errorMsg.length() > 0) {
         throw new CLPSRuntimeException(errorMsg.toString());
      } else {
         return super.beforeImport(excelObject);
      }
   }
}
