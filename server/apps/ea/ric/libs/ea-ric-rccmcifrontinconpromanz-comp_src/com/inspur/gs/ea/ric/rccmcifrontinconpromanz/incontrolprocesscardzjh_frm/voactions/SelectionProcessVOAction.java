package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesscardzjh_frm.voactions;

import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.api.lcp.LcpFactoryManagerUtils;
import com.inspur.edp.bef.api.parameter.retrieve.RespectiveRetrieveResult;
import com.inspur.edp.bef.api.parameter.retrieve.RetrieveParam;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import java.util.ArrayList;
import java.util.List;

public class SelectionProcessVOAction extends AbstractFSAction<Boolean> {
   private String id;
   private String[] ids;

   public SelectionProcessVOAction(String id, String[] ids) {
      this.id = id;
      this.ids = ids;
   }

   public void execute() {
      if (this.checkIds()) {
         this.setResult(false);
      } else {
         String str1 = "";

         for(String id : this.ids) {
            str1 = str1 + id + "\r\n";
         }

         str1 = str1.substring(0, str1.length() - 2);
         IStandardLcp lcpInControlOrg = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.rccmciinconpromanz.InConProManZ");
         IStandardLcp lcpProcessKeyStepsZ = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.rccmciinconpromanz.InConProManZ");
         EntityFilter filter = new EntityFilter();
         FilterCondition condition = new FilterCondition(0, "ID", ExpressCompareType.In, str1, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         ArrayList<FilterCondition> list = new ArrayList();
         list.add(condition);
         filter.setFilterConditions(list);
         List<IEntityData> dataInConProMan = lcpProcessKeyStepsZ.query(filter);
         IEntityData mergeRecordInfo = null;
         List<String> nodeCodes = new ArrayList();
         nodeCodes.add("RelatedProcessZ");
         List<String> hierachyIdList = new ArrayList();
         hierachyIdList.add(this.id);
         ModifyChangeDetail change = new ModifyChangeDetail(this.id);

         for(IEntityData mergeRecord : dataInConProMan) {
            IEntityData subinfo = lcpInControlOrg.retrieveDefaultChild(nodeCodes, hierachyIdList);
            ModifyChangeDetail subchange = new ModifyChangeDetail(subinfo.getID());
            subchange.getPropertyChanges().put("RelateProcessCode", mergeRecord.getValue("ProcessCode"));
            subchange.getPropertyChanges().put("RelateProcessName", mergeRecord.getValue("ProcessName"));
            subchange.getPropertyChanges().put("ProcessID", mergeRecord.getID());
            change.addChildChangeSet("RelatedProcessZ", subchange);
         }

         lcpInControlOrg.modify(change);
         this.setResult(true);
      }
   }

   private boolean checkIds() {
      RetrieveParam retrieveParam = new RetrieveParam();
      retrieveParam.setNeedLock(false);
      RespectiveRetrieveResult respectiveRetrieveResult = this.getContext().getLcp().retrieve(this.id, retrieveParam);
      IEntityData data = respectiveRetrieveResult.getData();
      IEntityDataCollection details = (IEntityDataCollection)data.getChilds().get("RelatedProcessZ");

      for(String id : this.ids) {
         for(IEntityData detail : details) {
            if (id.equals(detail.getValue("ProcessID"))) {
               return true;
            }
         }
      }

      return false;
   }
}
