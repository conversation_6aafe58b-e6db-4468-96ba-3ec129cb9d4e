package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesslistz_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.CafSession;
import java.util.ArrayList;

public class BeforeQueryVOAction extends BeforeQueryAction {
   public BeforeQueryVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      CafSession webSession = CAFContext.current.getCurrentSession();
      String userName = webSession.getUserName();
      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      FilterCondition filterCondition2 = new FilterCondition(0, "CreateInfo_CreatedBy", ExpressCompareType.Equal, userName, 0, ExpressRelationType.Empty, ExpressValueType.Value);
      list.add(filterCondition2);
      filter.addFilterConditions(list);
   }
}
