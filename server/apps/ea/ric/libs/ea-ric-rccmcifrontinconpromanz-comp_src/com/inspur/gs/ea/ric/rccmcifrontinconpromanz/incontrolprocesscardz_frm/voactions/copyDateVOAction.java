package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesscardz_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;

public class copyDateVOAction extends AbstractFSAction<String> {
   public void execute() {
      String id = (String)this.getContext().getVariableData().getValue("originId");
      IEntityData data = this.getLcp().retrieve(id).getData();
      IEntityData data1 = this.getLcp().retrieveDefault();
      ModifyChangeDetail changeDetail = new ModifyChangeDetail(data1.getID());
      changeDetail.getPropertyChanges().put("ProcessCode", data.getValue("ProcessCode"));
      changeDetail.getPropertyChanges().put("ProcessCode", data.getValue("ProcessName"));
      this.getLcp().modify(changeDetail);
      this.setResult(data1.getID());
   }
}
