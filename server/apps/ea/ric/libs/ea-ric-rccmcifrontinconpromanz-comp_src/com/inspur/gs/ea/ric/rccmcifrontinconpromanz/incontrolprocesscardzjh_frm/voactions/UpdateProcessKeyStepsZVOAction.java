package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesscardzjh_frm.voactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class UpdateProcessKeyStepsZVOAction extends AbstractFSAction<VoidActionResult> {
   private String ID;
   private IBqlExecuter bqlExecuter;

   public UpdateProcessKeyStepsZVOAction(String ID) {
      this.ID = ID;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("6a0620ef-bc69-4f56-b3c6-af89da5f14d8");
      IDbParameter[] Parameters = new IDbParameter[]{bqlExecuter.makeInParam("CL", this.ID)};
      String bql = "SELECT PROCESSCODE,PROCESSNAME FROM EAINCONPROMANZ WHERE EAINCONPROMANZ.ID  = @CL";
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);

      for(int i = 0; i < resultCheckOrgType.size(); ++i) {
         IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("PlanCode", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(0)), bqlExecuter.makeInParam("PlanName", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(1)), bqlExecuter.makeInParam("CL", this.ID)};
         String InsertBql = "UPDATE EAPROCESSKEYSTEPSZ set PROCESSCODE = :PlanCode, PROCESSNAME = :PlanName where EAPROCESSKEYSTEPSZ.PARENTID = :CL";
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs1 = new ArrayList();
         refEntityIDs1.add("fe8d4784-2e51-4f28-9b58-71d5fedff2ab");
         bqlExecuter.executeBqlStatement(InsertBql, refEntityIDs1, iDbParameters);
      }

   }
}
