package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesscardzjh_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class copyDateVOAction extends AbstractFSAction<String> {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      String id = (String)this.getContext().getVariableData().getValue("originalid");
      IEntityData data = this.getLcp().retrieve(id).getData();
      IEntityData data1 = this.getLcp().retrieveDefault();
      ModifyChangeDetail changeDetail = new ModifyChangeDetail(data1.getID());
      String FID = data1.getID();
      changeDetail.getPropertyChanges().put("OldId", id);
      changeDetail.getPropertyChanges().put("ProcessType", data.getValue("ProcessType"));
      changeDetail.getPropertyChanges().put("ProcessCode", data.getValue("ProcessCode"));
      changeDetail.getPropertyChanges().put("ProcessName", data.getValue("ProcessName"));
      changeDetail.getPropertyChanges().put("ProcessExplain", data.getValue("ProcessExplain"));
      changeDetail.getPropertyChanges().put("ProcessLevel", data.getValue("ProcessLevel"));
      changeDetail.getPropertyChanges().put("ProcessFunction", data.getValue("ProcessFunction"));
      changeDetail.getPropertyChanges().put("ProcessGuide", data.getValue("ProcessGuide"));
      changeDetail.getPropertyChanges().put("MainControlDept", data.getValue("MainControlDept"));
      changeDetail.getPropertyChanges().put("PartDept", data.getValue("PartDept"));
      changeDetail.getPropertyChanges().put("ScopeApplication", data.getValue("ScopeApplication"));
      changeDetail.getPropertyChanges().put("ManageObject", data.getValue("ManageObject"));
      changeDetail.getPropertyChanges().put("StepNum", data.getValue("StepNum"));
      int ve = Integer.parseInt(((String)data.getValue("VersionNumber")).substring(1, 2));
      String VersionNumber = "V" + (ve + 1) + ".0";
      changeDetail.getPropertyChanges().put("VersionNumber", VersionNumber);
      changeDetail.getPropertyChanges().put("IssueDate", data.getValue("IssueDate"));
      ValueObjModifyChangeDetail creatInfo = new ValueObjModifyChangeDetail();
      creatInfo.getPropertyChanges().put("CreatedOn", ((IAdministrativeInfo)data.getValue("CreateInfo")).getCreatedOn());
      creatInfo.getPropertyChanges().put("CreatedBy", ((IAdministrativeInfo)data.getValue("CreateInfo")).getCreatedBy());
      changeDetail.getPropertyChanges().put("CreateInfo", creatInfo);
      IEntityData relatedProcessZ = null;

      for(IEntityData relatedProcessZ1 : (IEntityDataCollection)data.getChilds().get("RelatedProcessZ")) {
         ArrayList<String> nodeCodes = new ArrayList();
         nodeCodes.add("RelatedProcessZ");
         ArrayList<String> list = new ArrayList();
         list.add(data1.getID());
         IEntityData data3 = this.getLcp().retrieveDefaultChild(nodeCodes, list);
         ModifyChangeDetail childChange = new ModifyChangeDetail(data3.getID());
         childChange.getPropertyChanges().put("RelateProcessCode", relatedProcessZ1.getValue("RelateProcessCode"));
         childChange.getPropertyChanges().put("RelateProcessName", relatedProcessZ1.getValue("RelateProcessName"));
         childChange.getPropertyChanges().put("ProcessID", relatedProcessZ1.getValue("ProcessID"));
         changeDetail.addChildChangeSet("RelatedProcessZ", childChange);
      }

      IEntityData processKeyStepsZ = null;

      for(IEntityData processKeyStepsZ1 : (IEntityDataCollection)data.getChilds().get("ProcessKeyStepsZ")) {
         ArrayList<String> nodeCodes = new ArrayList();
         nodeCodes.add("ProcessKeyStepsZ");
         ArrayList<String> list = new ArrayList();
         list.add(data1.getID());
         IEntityData data3 = this.getLcp().retrieveDefaultChild(nodeCodes, list);
         ModifyChangeDetail childChange = new ModifyChangeDetail(data3.getID());
         childChange.getPropertyChanges().put("StepName", processKeyStepsZ1.getValue("StepName"));
         AssociationInfo companyNameInfo = new AssociationInfo();
         companyNameInfo.setValue("CompanyName", ((AssociationInfo)processKeyStepsZ1.getValue("CompanyName")).getValue("CompanyName_ID"));
         companyNameInfo.setValue("CompanyName_Name", ((AssociationInfo)processKeyStepsZ1.getValue("CompanyName")).getValue("CompanyName_Name"));
         childChange.getPropertyChanges().put("CompanyName", processKeyStepsZ1.getValue("CompanyName"));
         AssociationInfo deptNameInfo = new AssociationInfo();
         deptNameInfo.setValue("DeptName", ((AssociationInfo)processKeyStepsZ1.getValue("DeptName")).getValue("DeptName_ID"));
         deptNameInfo.setValue("DeptName_Name", ((AssociationInfo)processKeyStepsZ1.getValue("DeptName")).getValue("DeptName_Name"));
         childChange.getPropertyChanges().put("DeptName", processKeyStepsZ1.getValue("DeptName"));
         childChange.getPropertyChanges().put("PostionName", processKeyStepsZ1.getValue("PostionName"));
         childChange.getPropertyChanges().put("WorkContent", processKeyStepsZ1.getValue("WorkContent"));
         childChange.getPropertyChanges().put("ControlledRecords", processKeyStepsZ1.getValue("ControlledRecords"));
         childChange.getPropertyChanges().put("WorkStandards", processKeyStepsZ1.getValue("WorkStandards"));
         childChange.getPropertyChanges().put("ProcessTigPoint", processKeyStepsZ1.getValue("ProcessTigPoint"));
         childChange.getPropertyChanges().put("MainControllPoint", processKeyStepsZ1.getValue("MainControllPoint"));
         childChange.getPropertyChanges().put("ReportTo", processKeyStepsZ1.getValue("ReportTo"));
         childChange.getPropertyChanges().put("ProcessCode", processKeyStepsZ1.getValue("ProcessCode"));
         childChange.getPropertyChanges().put("ProcessName", processKeyStepsZ1.getValue("ProcessName"));
         changeDetail.addChildChangeSet("ProcessKeyStepsZ", childChange);
      }

      IEntityData partDept = null;

      for(IEntityData partDept1 : (IEntityDataCollection)data.getChilds().get("ProcessPartDept")) {
         ArrayList<String> nodeCodes = new ArrayList();
         nodeCodes.add("ProcessPartDept");
         ArrayList<String> list = new ArrayList();
         list.add(data1.getID());
         IEntityData data3 = this.getLcp().retrieveDefaultChild(nodeCodes, list);
         ModifyChangeDetail childChange = new ModifyChangeDetail(data3.getID());
         AssociationInfo partDeptInfoInfo = new AssociationInfo();
         partDeptInfoInfo.setValue("PartDeptInfo", ((AssociationInfo)partDept1.getValue("PartDeptInfo")).getValue("PartDeptInfo_ID"));
         partDeptInfoInfo.setValue("PartDeptInfo_Name", ((AssociationInfo)partDept1.getValue("PartDeptInfo")).getValue("PartDeptInfo_Name"));
         childChange.getPropertyChanges().put("PartDeptInfo", partDept1.getValue("PartDeptInfo"));
         AssociationInfo partCompanyInfoInfo = new AssociationInfo();
         partCompanyInfoInfo.setValue("PartCompanyInfo", ((AssociationInfo)partDept1.getValue("PartCompanyInfo")).getValue("PartCompanyInfo_ID"));
         partCompanyInfoInfo.setValue("PartCompanyInfo_Name", ((AssociationInfo)partDept1.getValue("PartCompanyInfo")).getValue("PartCompanyInfo_Name"));
         childChange.getPropertyChanges().put("PartCompanyInfo", partDept1.getValue("PartCompanyInfo"));
         changeDetail.addChildChangeSet("ProcessPartDept", childChange);
      }

      this.getLcp().modify(changeDetail);
      this.setResult(data1.getID());
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("7aef13bd-d255-413b-a320-df32d6ba1ea8");
      IDbParameter[] Parameters = new IDbParameter[1];
      Parameters[0] = bqlExecuter.makeInParam("CL", id);
      String bql = "SELECT ATTACHFILEMANAGE,BILLID,UPDATEINFO_CREATEDON,UPDATEINFO_LASTCHANGEDON,UPDATEINFO_CREATEDBY,UPDATEINFO_LASTCHANGEDBY FROM EAATTACHMENT WHERE EAATTACHMENT.BILLID = @CL";
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);
      SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      df.format(new Date());
      WebSession webSession = CAFContext.current.getSession();
      String userName = webSession.getUserName();

      for(int i = 0; i < resultCheckOrgType.size(); ++i) {
         IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("ZID", UUID.randomUUID().toString()), bqlExecuter.makeInParam("ATTACHFILEMANAGE", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(0)), bqlExecuter.makeInParam("BILLID", FID), bqlExecuter.makeInParam("UPDATEINFO_CREATEDBY", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(4)), bqlExecuter.makeInParam("UPDATEINFO_CREATEDON", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(2)), bqlExecuter.makeInParam("UPDATEINFO_LASTCHANGEDBY", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(5)), bqlExecuter.makeInParam("UPDATEINFO_LASTCHANGEDON", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(3))};
         String InsertBql = "INSERT INTO EAATTACHMENT(ID, ATTACHFILEMANAGE, BILLID, UPDATEINFO_CREATEDBY,UPDATEINFO_CREATEDON, UPDATEINFO_LASTCHANGEDBY,UPDATEINFO_LASTCHANGEDON)values(:ZID, :ATTACHFILEMANAGE,:BILLID,:UPDATEINFO_CREATEDBY,:UPDATEINFO_CREATEDON,:UPDATEINFO_LASTCHANGEDBY,:UPDATEINFO_LASTCHANGEDON)";
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs1 = new ArrayList();
         refEntityIDs1.add("7aef13bd-d255-413b-a320-df32d6ba1ea8");
         bqlExecuter.executeBqlStatement(InsertBql, refEntityIDs1, iDbParameters);
      }

   }

   public static String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
