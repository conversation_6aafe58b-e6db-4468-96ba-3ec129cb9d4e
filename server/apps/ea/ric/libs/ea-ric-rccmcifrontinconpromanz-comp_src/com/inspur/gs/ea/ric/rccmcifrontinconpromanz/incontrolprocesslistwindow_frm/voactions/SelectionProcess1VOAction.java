package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesslistwindow_frm.voactions;

import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.api.lcp.LcpFactoryManagerUtils;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class SelectionProcess1VOAction extends AbstractFSAction<Boolean> {
   private String ID;
   private IBqlExecuter bqlExecuter;

   public SelectionProcess1VOAction(String ID) {
      this.ID = ID;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IStandardLcp lcpPurchase = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.rccmciicplan.InControlPlan");
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("6a0620ef-bc69-4f56-b3c6-af89da5f14d8");
      IDbParameter[] Parameters = new IDbParameter[]{bqlExecuter.makeInParam("CL", this.ID)};
      String bql = " SELECT COMPANYNAME,DEPTNAME FROM EAPROCESSKEYSTEPSZ WHERE EAPROCESSKEYSTEPSZ.PARENTID = @CL";
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);
      List<String> nodeCodes = new ArrayList();
      nodeCodes.add("InControlOrg");
      List<String> hierachyIdList = new ArrayList();
      hierachyIdList.add(this.ID);
      ModifyChangeDetail change = new ModifyChangeDetail(this.ID);

      for(int i = 0; i < resultCheckOrgType.size(); ++i) {
         IEntityData subinfo = lcpPurchase.retrieveDefaultChild(nodeCodes, hierachyIdList);
         ModifyChangeDetail subchange = new ModifyChangeDetail(subinfo.getID());
         subchange.getPropertyChanges().put("CompanyName", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(0));
         subchange.getPropertyChanges().put("DeptName", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(1));
         change.addChildChangeSet("InControlOrg", subchange);
      }

      lcpPurchase.modify(change);
      this.setResult(true);
   }
}
