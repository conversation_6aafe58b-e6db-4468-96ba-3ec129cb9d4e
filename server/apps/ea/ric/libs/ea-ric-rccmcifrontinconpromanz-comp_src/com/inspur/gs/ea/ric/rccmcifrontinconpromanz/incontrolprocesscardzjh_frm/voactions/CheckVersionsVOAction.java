package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesscardzjh_frm.voactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesslistz_frm.voactions.ResultException;
import io.iec.edp.caf.commons.exception.ExceptionLevel;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class CheckVersionsVOAction extends AbstractFSAction<VoidActionResult> {
   private String ID;
   private IBqlExecuter bqlExecuter;

   public CheckVersionsVOAction(String ID) {
      this.ID = ID;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("6a0620ef-bc69-4f56-b3c6-af89da5f14d8");
      IDbParameter[] Parameters = new IDbParameter[1];
      Parameters[0] = bqlExecuter.makeInParam("CL", this.ID);
      String bql = "SELECT ID FROM EAINCONPROMANZ EAINCONPROMANZ WHERE EAINCONPROMANZ.OLDID = :CL AND EAINCONPROMANZ.BILLSTATUS in ('0','1','3','4')";
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultInConProManZ = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);
      if (resultInConProManZ.size() > 0) {
         throw new ResultException("ric", "TJG-002", "请确认新版本流程审批通过！", (Exception)null, ExceptionLevel.Info, true);
      }
   }
}
