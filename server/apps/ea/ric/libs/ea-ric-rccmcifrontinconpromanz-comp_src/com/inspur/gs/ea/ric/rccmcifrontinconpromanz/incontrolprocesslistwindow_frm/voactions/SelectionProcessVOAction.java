package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesslistwindow_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;

public class SelectionProcessVOAction extends AbstractFSAction<ArrayList<IEntityData>> {
   private String ID;
   private IBqlExecuter bqlExecuter;

   public SelectionProcessVOAction(String ID) {
      this.ID = ID;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
   }
}
