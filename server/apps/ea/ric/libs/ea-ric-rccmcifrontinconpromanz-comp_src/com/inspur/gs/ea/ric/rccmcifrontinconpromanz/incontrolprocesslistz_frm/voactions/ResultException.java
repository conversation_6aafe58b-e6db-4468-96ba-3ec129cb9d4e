package com.inspur.gs.ea.ric.rccmcifrontinconpromanz.incontrolprocesslistz_frm.voactions;

import io.iec.edp.caf.commons.exception.CAFRuntimeException;
import io.iec.edp.caf.commons.exception.ExceptionLevel;

public class ResultException extends CAFRuntimeException {
   public ResultException(String serviceUnitCode, String exceptionCode, String message, Exception innerException, ExceptionLevel level, boolean bizException) {
      super(serviceUnitCode, exceptionCode, message, innerException, level, bizException);
   }
}
