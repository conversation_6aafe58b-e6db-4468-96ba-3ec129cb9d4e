package com.inspur.gs.ea.ric.riccscs.complisystemadjust_frm.voactions;

import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.api.lcp.LcpFactoryManagerUtils;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.CollectionUtils;

public class retriveIdVOAction extends AbstractFSAction<String> {
   private String orgid;
   private IBqlExecuter bqlExecuter;

   public retriveIdVOAction(String orgid) {
      this.orgid = orgid;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IStandardLcp lcp = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.riccscs.CompliSystem");
      EntityFilter filter = new EntityFilter();
      FilterCondition condition = new FilterCondition(0, "Organization", ExpressCompareType.Equal, this.orgid, 0, ExpressRelationType.Empty, ExpressValueType.Value);
      ArrayList<FilterCondition> list = new ArrayList();
      list.add(condition);
      filter.setFilterConditions(list);
      List<IEntityData> datalist = lcp.query(filter);
      if (CollectionUtils.isEmpty(datalist)) {
         IEntityData iEntityData = lcp.retrieveDefault();
         this.setResult(iEntityData.getID());
      } else {
         this.setResult(((IEntityData)datalist.get(0)).getID());
      }

   }
}
