package com.inspur.gs.ea.ric.ricclps.commoncmps;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.dxc.entity.data.DxcCommonData;
import com.inspur.edp.dxc.spi.components.BaseCustomValidateComponent;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

@GspComponent("8560e82e-eb30-4a7e-878f-bc383a6cd800")
public class CompliLinkManImport extends BaseCustomValidateComponent {
   private IBqlExecuter bqlExecuter;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void importData() {
      ArrayList<String> query = new ArrayList();
      DxcCommonData dxcCommonData = super.getData();

      for(int i = 0; i < dxcCommonData.getDataSet().size(); ++i) {
         AssociationInfo staffNameInfo = (AssociationInfo)dxcCommonData.getDataByCode(i, "StaffName");
         String id = staffNameInfo.getValue("StaffName").toString();
         IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", id)};
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         String QueryPostId = "SELECT BFPOSITIONEMPLOYEE.ID,BFPOSITIONEMPLOYEE.EMPLOYEE,BFPOSITIONEMPLOYEE.PARENTID,BFPOSITION.OFORGANIZATION AS PARENTID_OFORGANIZATION,BFPOSITION.NAME_CHS AS PARENTID_NAME_CHS,BFADMINORGANIZATION.NAME_CHS AS PARENTID_OFORGANIZATION_NAME_CHS FROM BFPOSITIONEMPLOYEE LEFT JOIN BFPOSITION BFPOSITION ON BFPOSITIONEMPLOYEE.PARENTID = BFPOSITION.ID LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION ON BFPOSITION.OFORGANIZATION = BFADMINORGANIZATION.ID WHERE 1=1  and BFPOSITIONEMPLOYEE.EMPLOYEE =:ID";
         List<DynamicResultRow> postIdResut = this.bqlExecuter.executeSelectStatement(QueryPostId, query, queryParams);
         if (CollectionUtils.isNotEmpty(postIdResut)) {
            AssociationInfo postNamesInfo = new AssociationInfo();
            postNamesInfo.setValue("PostNames", ((DynamicResultRow)postIdResut.get(0)).get("PARENTID"));
            dxcCommonData.pushDataByCode(i, "PostNames", postNamesInfo);
         }

         String unitId = "select o.id as oId,o.name_chs as oName,t.NAME_CHS as tName,o.treeinfo_path as path,e.Organization organization,e.EMAIL email,e.CONTACTINFO contractinfo from BFEMPLOYEE e left join BFADMINORGANIZATION o on  e.ORGANIZATION = o.id  left join BFMASTERORGANIZATIONTYPE t on o.orgtype  = t.id where e.id = :ID";
         List<DynamicResultRow> unitResut = this.bqlExecuter.executeSelectStatement(unitId, query, queryParams);
         if (CollectionUtils.isNotEmpty(unitResut)) {
            AssociationInfo organizationInfo = new AssociationInfo();
            organizationInfo.setValue("Organization", ((DynamicResultRow)unitResut.get(0)).get("Organization"));
            dxcCommonData.pushDataByCode(i, "Organization", organizationInfo);
            dxcCommonData.pushDataByCode(i, "Email", ((DynamicResultRow)unitResut.get(0)).get("email"));
            dxcCommonData.pushDataByCode(i, "TelNum", ((DynamicResultRow)unitResut.get(0)).get("contractinfo"));
            String pathId = String.valueOf(((DynamicResultRow)unitResut.get(0)).get("path"));
            String orgName = String.valueOf(((DynamicResultRow)unitResut.get(0)).get("oName"));

            List<DynamicResultRow> typeResut;
            for(String unitName = String.valueOf(((DynamicResultRow)unitResut.get(0)).get("tName") == null ? "" : ((DynamicResultRow)unitResut.get(0)).get("tName")); unitName.equals("部门"); orgName = String.valueOf(((DynamicResultRow)typeResut.get(0)).get("oName"))) {
               pathId = pathId.substring(0, pathId.length() - 4);
               String orgType = "select  t.NAME_CHS as tName,o.NAME_CHS as oName from BFADMINORGANIZATION o left join BFMASTERORGANIZATIONTYPE t  ON o.ORGTYPE = t.id where TREEINFO_PATH =:path";
               IDbParameter orgTypeParam = this.bqlExecuter.makeInParam("path", pathId);
               typeResut = this.bqlExecuter.executeSelectStatement(orgType, query, new IDbParameter[]{orgTypeParam});
               unitName = String.valueOf(((DynamicResultRow)typeResut.get(0)).get("tName"));
            }

            dxcCommonData.pushDataByCode(i, "Units", orgName);
         }

         String globalParam = super.getGlobalParam();
         if ("FX".equals(globalParam)) {
            dxcCommonData.pushDataByCode(i, "FuncType", "FX");
         } else if ("NK".equals(globalParam)) {
            dxcCommonData.pushDataByCode(i, "FuncType", "NK");
         } else {
            dxcCommonData.pushDataByCode(i, "FuncType", "HG");
         }
      }

   }
}
