package com.inspur.gs.ea.ric.ricclps.complilinkmanlac_frm.voactions;

import com.inspur.edp.bff.spi.AbstractHelpAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.edp.web.help.api.LookupQueryParam;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class FilterPositionVOAction extends AbstractHelpAction {
   private IBqlExecuter bqlExecuter;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void beforeHelp(LookupQueryParam lookupQueryParam) {
      String DeptId = (String)this.getContext().getVariableData().getValue("deptId");
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "EACompliLinkmanType")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> result = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(result)) {
         if ("0".equals(((DynamicResultRow)result.get(0)).get("configvalue"))) {
            if (DeptId != null) {
               EntityFilter filter = new EntityFilter();
               FilterCondition condition = new FilterCondition(0, "Organization", ExpressCompareType.Equal, DeptId, 0, ExpressRelationType.Empty, ExpressValueType.Value);
               ArrayList<FilterCondition> list = new ArrayList();
               list.add(condition);
               filter.addFilterConditions(list);
               lookupQueryParam.setFilter(filter);
            }
         } else if ("2".equals(((DynamicResultRow)result.get(0)).get("configvalue")) && DeptId != null) {
            String entitySql = "select treeinfo_path from bfadminorganization where id = :orgid";
            IDbParameter[] entityPara = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("orgid", DeptId)};
            this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            List<DynamicResultRow> orgs = this.bqlExecuter.executeSelectStatement(entitySql, planIDs, entityPara);
            if (orgs == null || orgs.size() == 0) {
               return;
            }

            String treepath = (String)((DynamicResultRow)orgs.get(0)).get("treeinfo_path");
            String orgsql = "(select id from bfadminorganization where treeinfo_path like '" + treepath.trim() + "%')";
            EntityFilter filter = new EntityFilter();
            FilterCondition condition = new FilterCondition(0, "Organization", ExpressCompareType.In, orgsql, 0, ExpressRelationType.Empty, ExpressValueType.Value);
            ArrayList<FilterCondition> list = new ArrayList();
            list.add(condition);
            filter.setFilterConditions(list);
            lookupQueryParam.setFilter(filter);
         }
      }

   }
}
