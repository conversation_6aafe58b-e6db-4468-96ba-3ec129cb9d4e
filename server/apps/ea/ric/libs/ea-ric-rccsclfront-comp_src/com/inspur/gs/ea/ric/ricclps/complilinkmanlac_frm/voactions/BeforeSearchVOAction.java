package com.inspur.gs.ea.ric.ricclps.complilinkmanlac_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.commons.utils.StringUtils;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class BeforeSearchVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public BeforeSearchVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      IBqlExecuter iBqlExecuter = this.getBqlExecuter();
      String DeptId = (String)this.getContext().getVariableData().getValue("deptId");
      String menutype = this.getContext().getVariableData().getValue("menutype") == null ? "HG" : (String)this.getContext().getVariableData().getValue("menutype");
      if (StringUtils.isEmpty(menutype)) {
         menutype = "HG";
      }

      if (DeptId == null) {
         DeptId = "";
      }

      ArrayList<String> refEntityIDs = new ArrayList();
      String queryDeptCode = "select TREEINFO_PATH from BFADMINORGANIZATION where id =:id";
      IDbParameter iDbParameters = iBqlExecuter.makeInParam("id", DeptId);
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(queryDeptCode, refEntityIDs, new IDbParameter[]{iDbParameters});
      List<DynamicResultRow> deptData = null;
      if (mainData.size() != 0) {
         String queryDepts = "select id from BFADMINORGANIZATION where TREEINFO_PATH like :treeinfo_path";
         IDbParameter paramCode = iBqlExecuter.makeInParam("treeinfo_path", ((DynamicResultRow)mainData.get(0)).get("treeinfo_path") + "%");
         deptData = iBqlExecuter.executeSelectStatement(queryDepts, refEntityIDs, new IDbParameter[]{paramCode});
      }

      String str = "";
      if (deptData != null) {
         for(DynamicResultRow res : deptData) {
            str = str + res.getValues().get(0).toString() + "\r\n";
         }

         str = str.substring(0, str.length() - 2);
      }

      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      FilterCondition filterCondition1 = new FilterCondition(1, "Organization", ExpressCompareType.In, str, 1, ExpressRelationType.And, ExpressValueType.Value);
      if ("HG".equals(menutype)) {
         FilterCondition filterCondition2 = new FilterCondition(1, "FuncType", ExpressCompareType.Equal, menutype, 0, ExpressRelationType.Or, ExpressValueType.Value);
         FilterCondition filterCondition3 = new FilterCondition(0, "FuncType", ExpressCompareType.Is, "null", 0, ExpressRelationType.Or, ExpressValueType.Value);
         FilterCondition filterCondition4 = new FilterCondition(0, "FuncType", ExpressCompareType.Equal, "", 1, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition1);
         list.add(filterCondition2);
         list.add(filterCondition3);
         list.add(filterCondition4);
      } else {
         FilterCondition filterCondition2 = new FilterCondition(1, "FuncType", ExpressCompareType.Equal, menutype, 1, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition1);
         list.add(filterCondition2);
      }

      filter.addFilterConditions(list);
   }
}
