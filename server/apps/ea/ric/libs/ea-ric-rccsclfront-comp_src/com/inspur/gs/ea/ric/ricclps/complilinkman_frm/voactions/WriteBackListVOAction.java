package com.inspur.gs.ea.ric.ricclps.complilinkman_frm.voactions;

import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.api.lcp.LcpFactoryManagerUtils;
import com.inspur.edp.bef.api.parameter.retrieve.RetrieveParam;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.gs.bf.df.employee.employee.entity.IEmployee;
import com.inspur.gs.ea.ric.ricclps.complilinkman.entity.ICompliLinkman;
import java.util.ArrayList;
import java.util.List;

public class WriteBackListVOAction extends AbstractFSAction<ArrayList<IEntityData>> {
   private String[] ids;

   public WriteBackListVOAction(String[] ids) {
      this.ids = ids;
   }

   public void execute() {
      IStandardLcp lcpPurchase = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.ricclps.CompliLinkman");
      IStandardLcp employee = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("Employee");
      String str1 = "";

      for(String id : this.ids) {
         str1 = str1 + id + "\r\n";
      }

      if (str1 != "" && str1 != null) {
         str1 = str1.substring(0, str1.length() - 2);
         new RetrieveParam();
         EntityFilter filter = new EntityFilter();
         FilterCondition condition = new FilterCondition(0, "ID", ExpressCompareType.In, str1, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         ArrayList<FilterCondition> list = new ArrayList();
         list.add(condition);
         filter.setFilterConditions(list);
         List<IEntityData> dataCol = employee.query(filter);
         ArrayList<IEntityData> compliManList = new ArrayList();
         IEmployee mergeRecordInfo = null;
         ICompliLinkman PsnlAPInfoEntityInfo = null;

         for(IEntityData mergeRecord : dataCol) {
            mergeRecordInfo = (IEmployee)mergeRecord;
            IEntityData purchaseRecord = lcpPurchase.retrieveDefault();
            purchaseRecord.setValue("StaffCode", mergeRecordInfo.getCode().getDFCode());
            purchaseRecord.setValue("StaffName", mergeRecordInfo.getDisplayName());
            compliManList.add(purchaseRecord);
         }

         this.setResult(compliManList);
      }

   }
}
