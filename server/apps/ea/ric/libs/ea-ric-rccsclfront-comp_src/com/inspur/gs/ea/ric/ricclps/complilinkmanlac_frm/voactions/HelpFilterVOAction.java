package com.inspur.gs.ea.ric.ricclps.complilinkmanlac_frm.voactions;

import com.inspur.edp.bff.spi.AbstractHelpAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.web.help.api.LookupQueryParam;
import java.util.ArrayList;

public class HelpFilterVOAction extends AbstractHelpAction {
   public void beforeHelp(LookupQueryParam lookupQueryParam) {
      String DeptId = (String)this.getContext().getVariableData().getValue("deptId");
      if (DeptId != null) {
         EntityFilter filter = new EntityFilter();
         FilterCondition condition = new FilterCondition(0, "Organization", ExpressCompareType.Equal, DeptId, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         ArrayList<FilterCondition> list = new ArrayList();
         list.add(condition);
         filter.addFilterConditions(list);
         lookupQueryParam.setFilter(filter);
      }

   }
}
