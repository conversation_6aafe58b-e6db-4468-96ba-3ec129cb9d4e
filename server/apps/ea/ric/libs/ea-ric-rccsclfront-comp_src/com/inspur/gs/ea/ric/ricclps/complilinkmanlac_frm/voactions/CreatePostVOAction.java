package com.inspur.gs.ea.ric.ricclps.complilinkmanlac_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class CreatePostVOAction extends AbstractFSAction<String> {
   private String postId;
   private IBqlExecuter bqlExecuter;

   public CreatePostVOAction(String postId) {
      this.postId = postId;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      String postName = "";
      if (this.postId != "" || this.postId != null) {
         IBqlExecuter bqlExecuter = this.getBqlExecuter();
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         String QueryPostId = "SELECT BFPOSITIONEMPLOYEE.ID,BFPOSITIONEMPLOYEE.EMPLOYEE,BFPOSITIONEMPLOYEE.PARENTID,BFPOSITION.OFORGANIZATION AS PARENTID_OFORGANIZATION,BFPOSITION.NAME_CHS AS PARENTID_NAME_CHS,BFADMINORGANIZATION.NAME_CHS AS PARENTID_OFORGANIZATION_NAME_CHS FROM BFPOSITIONEMPLOYEE LEFT JOIN BFPOSITION BFPOSITION ON BFPOSITIONEMPLOYEE.PARENTID = BFPOSITION.ID LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION ON BFPOSITION.OFORGANIZATION = BFADMINORGANIZATION.ID WHERE 1=1  and BFPOSITIONEMPLOYEE.EMPLOYEE =:postId";
         IDbParameter param = bqlExecuter.makeInParam("postId", this.postId);
         ArrayList<String> query = new ArrayList();
         List<DynamicResultRow> postIdResut = bqlExecuter.executeSelectStatement(QueryPostId, query, new IDbParameter[]{param});
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         String unitId = "select o.id as oId,o.name_chs as oName,t.NAME_CHS as tName,o.treeinfo_path as path  from BFEMPLOYEE e left join BFADMINORGANIZATION o on  e.ORGANIZATION = o.id  left join BFMASTERORGANIZATIONTYPE t on o.orgtype  = t.id where e.id = :postId";
         IDbParameter unitParam = bqlExecuter.makeInParam("postId", this.postId);
         List<DynamicResultRow> unitResut = bqlExecuter.executeSelectStatement(unitId, query, new IDbParameter[]{unitParam});
         String pathId = String.valueOf(((DynamicResultRow)unitResut.get(0)).get("path"));
         String orgName = String.valueOf(((DynamicResultRow)unitResut.get(0)).get("oName"));

         List<DynamicResultRow> typeResut;
         for(String unitName = String.valueOf(((DynamicResultRow)unitResut.get(0)).get("tName") == null ? "" : ((DynamicResultRow)unitResut.get(0)).get("tName")); unitName.equals("部门"); orgName = String.valueOf(((DynamicResultRow)typeResut.get(0)).get("oName"))) {
            pathId = pathId.substring(0, pathId.length() - 4);
            String orgType = "select  t.NAME_CHS as tName,o.NAME_CHS as oName from BFADMINORGANIZATION o left join BFMASTERORGANIZATIONTYPE t  ON o.ORGTYPE = t.id where TREEINFO_PATH =:path";
            IDbParameter orgTypeParam = bqlExecuter.makeInParam("path", pathId);
            typeResut = bqlExecuter.executeSelectStatement(orgType, query, new IDbParameter[]{orgTypeParam});
            unitName = String.valueOf(((DynamicResultRow)typeResut.get(0)).get("tName"));
         }

         if (!postIdResut.isEmpty()) {
            postName = ((DynamicResultRow)postIdResut.get(0)).get("PARENTID_NAME_CHS") + "/" + ((DynamicResultRow)postIdResut.get(0)).get("PARENTID") + "/" + orgName;
         }
      }

      this.setResult(postName);
   }
}
