package com.inspur.gs.ea.ric.ricclps.complilinkmanlac_frm.voactions;

import com.inspur.edp.bff.spi.AbstractHelpAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.web.help.api.LookupQueryParam;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import java.util.ArrayList;

public class FilterPostVOAction extends AbstractHelpAction {
   public void beforeHelp(LookupQueryParam lookupQueryParam) {
      WebSession webSession = CAFContext.current.getSession();
      String orgName = webSession.getSysOrgId();
      EntityFilter filter = new EntityFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      FilterCondition filterCondition = new FilterCondition(0, "oforganization", ExpressCompareType.Equal, orgName, 0, ExpressRelationType.Empty, ExpressValueType.Value);
      list.add(filterCondition);
      filter.addFilterConditions(list);
      lookupQueryParam.setFilter(filter);
   }
}
