package com.inspur.gs.ea.ric.rccsim.organiztionserach_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.AfterQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.pathhierarchyinfo.entity.IPathHierarchyInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class searchFilterVOAction extends AfterQueryAction {
   private IBqlExecuter bqlExecuter;
   private static final String ONE = "1";

   public searchFilterVOAction(QueryContext context) {
      super(context);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      String paramValue = "";
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "EAInstitutionOrg")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultParam = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(resultParam)) {
         paramValue = this.valueOf(((DynamicResultRow)resultParam.get(0)).get("configvalue"));
      }

      if ("1".equals(paramValue)) {
         ArrayList<IEntityData> results = this.getQueryContext().getQueryResult();

         for(int i = 0; i < results.size(); ++i) {
            IEntityData result = (IEntityData)results.get(i);
            IPathHierarchyInfo treeInfo = (IPathHierarchyInfo)result.getValue("TreeInfo");
            if (!treeInfo.getIsDetail()) {
               IDbParameter[] filterParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("OwnerId", result.getValue("ID").toString())};
               String filterSql = "SELECT ID FROM BFADMINORGANIZATION WHERE OWNERID = :OwnerId AND STATE_ISENABLED = '1' AND ORGTYPE = '9edf25d1-b991-4dde-90b9-30145422d24d'";
               List<DynamicResultRow> filterResult = this.bqlExecuter.executeSelectStatement(filterSql, planIDs, filterParams);
               if (CollectionUtils.isEmpty(filterResult)) {
                  treeInfo.setIsDetail(true);
                  result.setValue("TreeInfo", treeInfo);
                  results.set(i, result);
                  this.getQueryContext().setQueryResult(results);
               }
            }
         }
      }

      String id = "";
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
