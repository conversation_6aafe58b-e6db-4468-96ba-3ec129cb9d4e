package com.inspur.gs.ea.ric.rccsim.listenerTask;

import com.inspur.edp.wf.engine.event.IWorkItemEventListener;

public class ListenerTask implements IWorkItemEventListener {
   public void workItemCreatedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
      String processDefinitionId = workItemEventArgs.getWorkflowContext().getProcessDefinitionId();
      String activityDefinitionId = workItemEventArgs.getWorkflowContext().getCurrentActivityInstance().getActivityDefinitionId();
      String activityDefinitionName = workItemEventArgs.getWorkflowContext().getCurrentActivityInstance().getActivityDefinitionName();
      String precursorActivityInstanceId = workItemEventArgs.getWorkflowContext().getCurrentActivityInstance().getPrecursorActivityInstanceId();
      String BizActivityId = workItemEventArgs.getWorkflowContext().getCurrentActivityInstance().getBizActivityId();

      for(int i = 0; i <= 10; ++i) {
      }

   }

   public void workItemCompletedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }

   public void workItemSuspendedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }

   public void workItemResumedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }

   public void workItemAbortedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }

   public void workItemRetrievedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }

   public void workItemClaimedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }

   public void workItemUnclaimedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs workItemEventArgs) {
   }
}
