package com.inspur.gs.ea.ric.rccsim.listenerTask;

import com.inspur.edp.wf.runtime.entity.WorkItemAction;
import java.util.List;
import java.util.Map;

public class WorkItemEventArgs {
   private WorkContext workflowContext;
   private List<WorkItemAction> workItemActionList;
   private Map<String, Object> workItemVariables;

   public WorkItem getCurrentWorkItem() {
      return (WorkItem)this.workflowContext.getCurrentWorkItem();
   }
}
