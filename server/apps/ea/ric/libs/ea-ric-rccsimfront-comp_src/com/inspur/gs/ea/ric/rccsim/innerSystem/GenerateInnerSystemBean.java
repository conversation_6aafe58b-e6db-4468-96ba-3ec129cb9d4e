package com.inspur.gs.ea.ric.rccsim.innerSystem;

import io.iec.edp.caf.rest.RESTEndpoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GenerateInnerSystemBean {
   @Bean
   public RESTEndpoint innerInstitution() {
      InnerSystemService service = new InnerSystemServiceImp();
      return new RESTEndpoint("/ea/ric/v1.0", new Object[]{service});
   }
}
