package com.inspur.gs.ea.ric.rccsim.institutionmgrlist_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class BeforeSearchVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;

   public BeforeSearchVOAction(QueryContext context) {
      super(context);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      String DeptId = this.valueOf(this.getContext().getVariableData().getValue("deptIds"));
      String paramValue = "";
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "EARuleQrOrgLevel")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultParam = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(resultParam)) {
         paramValue = this.valueOf(((DynamicResultRow)resultParam.get(0)).get("configvalue"));
      }

      if ("".equals(DeptId)) {
         EntityFilter filter = this.getQueryContext().getFilter();
         ArrayList<FilterCondition> list = new ArrayList();
         FilterCondition filterCondition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "Out", 0, ExpressRelationType.And, ExpressValueType.Value);
         FilterCondition filterCondition1 = new FilterCondition(0, "billState", ExpressCompareType.Equal, "Approved", 0, ExpressRelationType.And, ExpressValueType.Value);
         FilterCondition filterCondition3 = new FilterCondition(0, "isLatest", ExpressCompareType.Equal, "S", 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         list.add(filterCondition1);
         list.add(filterCondition3);
         filter.addFilterConditions(list);
      } else {
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs = new ArrayList();
         String queryDeptCode = "";
         queryDeptCode = "select TREEINFO_PATH from BFADMINORGANIZATION where id =:id";
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("id", DeptId);
         List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(queryDeptCode, refEntityIDs, new IDbParameter[]{iDbParameters});
         List<DynamicResultRow> deptData = null;
         if (mainData.size() != 0) {
            String queryDepts = "select id from BFADMINORGANIZATION where TREEINFO_PATH like :treeinfo_path";
            IDbParameter paramCode = iBqlExecuter.makeInParam("treeinfo_path", ((DynamicResultRow)mainData.get(0)).get("treeinfo_path") + "%");
            deptData = iBqlExecuter.executeSelectStatement(queryDepts, refEntityIDs, new IDbParameter[]{paramCode});
         }

         EntityFilter filter = this.getQueryContext().getFilter();
         ArrayList<FilterCondition> list = new ArrayList();
         String str = "";
         if (deptData != null) {
            for(DynamicResultRow res : deptData) {
               str = str + res.getValues().get(0).toString() + "\r\n";
            }

            str = str.substring(0, str.length() - 2);
         }

         FilterCondition filterCondition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "Out", 0, ExpressRelationType.And, ExpressValueType.Value);
         FilterCondition filterCondition1 = new FilterCondition(0, "billState", ExpressCompareType.Equal, "Approved", 0, ExpressRelationType.And, ExpressValueType.Value);
         FilterCondition filterCondition2 = null;
         FilterCondition filterCondition3 = new FilterCondition(0, "isLatest", ExpressCompareType.Equal, "S", 0, ExpressRelationType.Empty, ExpressValueType.Value);
         if ("0".equals(paramValue)) {
            filterCondition2 = new FilterCondition(1, "mgrDepart", ExpressCompareType.In, str, 1, ExpressRelationType.And, ExpressValueType.Value);
         } else {
            filterCondition2 = new FilterCondition(0, "mgrDepart", ExpressCompareType.Equal, DeptId, 0, ExpressRelationType.And, ExpressValueType.Value);
         }

         list.add(filterCondition);
         list.add(filterCondition1);
         list.add(filterCondition2);
         list.add(filterCondition3);
         filter.addFilterConditions(list);
      }
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
