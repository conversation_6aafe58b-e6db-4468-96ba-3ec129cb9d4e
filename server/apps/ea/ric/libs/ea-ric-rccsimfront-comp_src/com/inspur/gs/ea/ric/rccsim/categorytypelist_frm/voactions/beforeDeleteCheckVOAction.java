package com.inspur.gs.ea.ric.rccsim.categorytypelist_frm.voactions;

import com.inspur.edp.bef.api.exceptions.BefException;
import com.inspur.edp.bef.entity.exception.ExceptionLevel;
import com.inspur.edp.bff.api.manager.context.DeleteContext;
import com.inspur.edp.bff.spi.action.delete.BeforeDeleteAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class beforeDeleteCheckVOAction extends BeforeDeleteAction {
   private IBqlExecuter bqlExecuter;

   public beforeDeleteCheckVOAction(DeleteContext context) {
      super(context);
   }

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      String ID = this.getDeleteContext().getDataId();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Category", ID)};
      String sqlselect1 = "select ID from EAInstitutionMgr where Category = :Category ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultParam1 = this.bqlExecuter.executeSelectStatement(sqlselect1, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(resultParam1)) {
         throw new BefException("002", "数据被【外部制度管理】引用，无法删除。", (RuntimeException)null, ExceptionLevel.Info);
      }
   }
}
