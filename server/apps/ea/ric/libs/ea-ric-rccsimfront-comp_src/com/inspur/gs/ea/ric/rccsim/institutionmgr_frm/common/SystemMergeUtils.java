package com.inspur.gs.ea.ric.rccsim.institutionmgr_frm.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;

public final class SystemMergeUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }

   public static String getSystemId(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "SystemId");
   }

   public static void setSystemId(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "SystemId", propertyValue);
   }

   public static String getcode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "code");
   }

   public static void setcode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "code", propertyValue);
   }

   public static String getname(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "name");
   }

   public static void setname(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "name", propertyValue);
   }

   public static AssoInfoBase getPublishDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "PublishDepart");
   }

   public static void setPublishDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "PublishDepart", propertyValue);
   }

   public static String getruleType(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ruleType");
   }

   public static void setruleType(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ruleType", propertyValue);
   }

   public static String getruleVersion(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ruleVersion");
   }

   public static void setruleVersion(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ruleVersion", propertyValue);
   }
}
