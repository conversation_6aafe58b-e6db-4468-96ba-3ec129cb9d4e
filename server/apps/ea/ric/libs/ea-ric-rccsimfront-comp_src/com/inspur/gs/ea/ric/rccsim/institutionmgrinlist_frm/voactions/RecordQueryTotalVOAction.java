package com.inspur.gs.ea.ric.rccsim.institutionmgrinlist_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

public class RecordQueryTotalVOAction extends AbstractFSAction<Boolean> {
   private String id;
   private IBqlExecuter bqlExecuter;

   public RecordQueryTotalVOAction(String id) {
      this.id = id;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      if (StringUtils.isNotEmpty(this.id)) {
         ArrayList<String> planIds = new ArrayList();
         IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("id", this.id)};
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         String sqlSel = "select id,QueryNum from EAInstitutionalTotal where InstitutionalID = :id";
         List<DynamicResultRow> result = this.bqlExecuter.executeSelectStatement(sqlSel, planIds, selectParams);
         if (CollectionUtils.isNotEmpty(result)) {
            int queryNum = 0;
            if (null != ((DynamicResultRow)result.get(0)).get("QueryNum")) {
               queryNum = Integer.parseInt(((DynamicResultRow)result.get(0)).get("QueryNum").toString());
            }

            IDbParameter[] updateParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("id", this.id), this.getBqlExecuter().makeInOutParam("QueryNum", queryNum + 1)};
            String sqlUpdate = "update EAInstitutionalTotal set QueryNum = :QueryNum where InstitutionalID = :id";
            this.bqlExecuter.executeBqlStatement(sqlUpdate, planIds, updateParams);
         } else {
            String newId = UUID.randomUUID().toString();
            IDbParameter[] insertParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("id", newId), this.getBqlExecuter().makeInOutParam("InstitutionalID", this.id), this.getBqlExecuter().makeInOutParam("QueryNum", 1), this.getBqlExecuter().makeInOutParam("Type", 0)};
            String sqlInsert = "insert into EAInstitutionalTotal(id,InstitutionalID,QueryNum,Type) values(:id,:InstitutionalID,:QueryNum,:Type)";
            this.bqlExecuter.executeBqlStatement(sqlInsert, planIds, insertParams);
         }

         this.setResult(true);
      }

   }
}
