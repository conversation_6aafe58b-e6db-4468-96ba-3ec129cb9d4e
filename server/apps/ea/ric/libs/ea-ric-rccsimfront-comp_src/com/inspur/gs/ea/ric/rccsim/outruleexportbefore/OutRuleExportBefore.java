package com.inspur.gs.ea.ric.rccsim.outruleexportbefore;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.dataie.entity.model.DataExchangeModel;
import com.inspur.edp.dxc.spi.components.BaseBeforeExportFlowsComponent;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.StringUtils;

@GspComponent("d784d237-efc0-44d8-8d4b-93c94e26e4f5")
public class OutRuleExportBefore extends BaseBeforeExportFlowsComponent {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void exportBefore() {
      DataExchangeModel model = super.getModel();
      EntityFilter filter = super.getFilter();
      String DeptId = super.getGlobalParam();
      if (!StringUtils.isEmpty(DeptId)) {
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs = new ArrayList();
         String queryDeptCode = "";
         queryDeptCode = "select TREEINFO_PATH from BFADMINORGANIZATION where id =:id";
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("id", DeptId);
         List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(queryDeptCode, refEntityIDs, new IDbParameter[]{iDbParameters});
         List<DynamicResultRow> deptData = null;
         if (mainData.size() != 0) {
            String queryDepts = "select id from BFADMINORGANIZATION where TREEINFO_PATH like :treeinfo_path";
            IDbParameter paramCode = iBqlExecuter.makeInParam("treeinfo_path", ((DynamicResultRow)mainData.get(0)).get("treeinfo_path") + "%");
            deptData = iBqlExecuter.executeSelectStatement(queryDepts, refEntityIDs, new IDbParameter[]{paramCode});
         }

         ArrayList<FilterCondition> list = new ArrayList();
         String str = "";
         if (deptData != null) {
            for(DynamicResultRow res : deptData) {
               str = str + res.getValues().get(0).toString() + "\r\n";
            }

            str = str.substring(0, str.length() - 2);
         }

         FilterCondition filterCondition = new FilterCondition(0, "mgrDepart", ExpressCompareType.In, str, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         filter.addFilterConditions(list);
      }
   }
}
