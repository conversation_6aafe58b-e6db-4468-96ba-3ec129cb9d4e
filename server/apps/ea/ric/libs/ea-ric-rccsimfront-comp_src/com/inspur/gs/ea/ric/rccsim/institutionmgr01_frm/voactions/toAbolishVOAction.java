package com.inspur.gs.ea.ric.rccsim.institutionmgr01_frm.voactions;

import com.inspur.edp.bef.api.lcp.ILcpFactory;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.common.commonudt.billstate.entity.BillStateEnum;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.Date;

public class toAbolishVOAction extends AbstractFSAction<String> {
   private String id;
   private IBqlExecuter bqlExecuter;

   public toAbolishVOAction(String id) {
      this.id = id;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      WebSession webSession = CAFContext.current.getSession();
      String userName = webSession.getUserName();
      ModifyChangeDetail change = new ModifyChangeDetail(this.id);
      ValueObjModifyChangeDetail billStateChange = new ValueObjModifyChangeDetail();
      billStateChange.getPropertyChanges().put("BillState", BillStateEnum.Billing);
      change.getPropertyChanges().put("BillState", billStateChange);
      ValueObjModifyChangeDetail billProcess = new ValueObjModifyChangeDetail();
      billProcess.getPropertyChanges().put("ProcessInstance", "");
      change.getPropertyChanges().put("BillProcess", billProcess);
      ValueObjModifyChangeDetail updateInfoChange = new ValueObjModifyChangeDetail();
      updateInfoChange.getPropertyChanges().put("LastChangedBy", userName);
      updateInfoChange.getPropertyChanges().put("LastChangedOn", new Date());
      change.getPropertyChanges().put("UpdateInfo", updateInfoChange);
      AssociationInfo institutionalPlan = new AssociationInfo();
      institutionalPlan.setValue("InstitutionalPlan", "");
      institutionalPlan.setValue("InstitutionalPlan_DocName", "");
      change.getPropertyChanges().put("InstitutionalPlan", institutionalPlan);
      change.getPropertyChanges().put("isLatest", "F");
      IStandardLcp lcp = ((ILcpFactory)SpringBeanUtils.getBean(ILcpFactory.class)).createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      lcp.modify(change);
   }
}
