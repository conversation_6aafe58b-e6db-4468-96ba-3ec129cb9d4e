package com.inspur.gs.ea.ric.rccsim.innerSystem;

import com.alibaba.fastjson.JSON;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InnerSystemServiceImp implements InnerSystemService {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public String getDataByOrgId(String ids) {
      String[] arr = ids.split(",");
      List<Map<String, Object>> list = new ArrayList();

      for(String arrItem : arr) {
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("7cdc0646-2a4d-474e-be60-338f4af635ca");
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("CL", arrItem);
         String tempBql = "SELECT ECT.NAME AS Category, RES.CODE, EDL.NAME AS DOCLEVEL, RES.KEYWORD, RES.MGRDEPART , RES.NAME, RES.OFFICIALDOCNUM, RES.PUBLISHDATE01 AS PUBLISHDATE, BAO.NAME_CHS AS PUBLISHDEPART , CASE  WHEN RES.RANGE = '0' THEN '集团本部' WHEN RES.RANGE = '1' THEN '下属单位' WHEN RES.RANGE = '2' THEN '集团本部及下属单位' END AS RANGE, RES.REGISTDEPART, RES.RULEVERSION , CASE  WHEN RES.SLAVESYSTEM = '0' THEN '党群类' WHEN RES.SLAVESYSTEM = '1' THEN '行政类' END AS SLAVESYSTEM FROM EAInstitutionMgr RES LEFT JOIN eaCategoryType ECT ON RES.CATEGORY = ECT.ID LEFT JOIN EADocLevel EDL ON RES.DOCLEVEL = EDL.ID LEFT JOIN BFADMINORGANIZATION BAO ON RES.DOCLEVEL = BAO.ID WHERE RES.ID IN ( SELECT DISTINCT ID FROM EAInstitutionMgr WHERE BILLSTATE = '2' AND RULETYPE='0' AND ISLATEST='1' \nAND PUBLISHDEPART = :CL )";

         for(DynamicResultRow item : iBqlExecuter.executeSelectStatement(tempBql, refEntityIDs, new IDbParameter[]{iDbParameters})) {
            Map<String, Object> map = new HashMap();

            for(int i = 0; i < item.getValues().size(); ++i) {
               if (!"".equals(item.get_columnNames().get(i)) && item.get_columnNames().get(i) != null) {
                  map.put(item.get_columnNames().get(i), item.getValues().get(i));
               }
            }

            list.add(map);
         }
      }

      return JSON.toJSONString(list);
   }

   public String getDataByUserId(String ids) {
      String[] arr = ids.split(",");
      List<Map<String, Object>> list = new ArrayList();

      for(String arrItem : arr) {
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("7cdc0646-2a4d-474e-be60-338f4af635ca");
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("CL", arrItem);
         String tempBql = "SELECT ECT.NAME AS Category, RES.CODE, EDL.NAME AS DOCLEVEL, RES.KEYWORD, RES.MGRDEPART , RES.NAME, RES.OFFICIALDOCNUM, RES.PUBLISHDATE01 AS PUBLISHDATE, BAO.NAME_CHS AS PUBLISHDEPART , CASE  WHEN RES.RANGE = '0' THEN '集团本部' WHEN RES.RANGE = '1' THEN '下属单位' WHEN RES.RANGE = '2' THEN '集团本部及下属单位' END AS RANGE, RES.REGISTDEPART, RES.RULEVERSION , CASE  WHEN RES.SLAVESYSTEM = '0' THEN '党群类' WHEN RES.SLAVESYSTEM = '1' THEN '行政类' END AS SLAVESYSTEM FROM EAInstitutionMgr RES LEFT JOIN eaCategoryType ECT ON RES.CATEGORY = ECT.ID LEFT JOIN EADocLevel EDL ON RES.DOCLEVEL = EDL.ID LEFT JOIN BFADMINORGANIZATION BAO ON RES.DOCLEVEL = BAO.ID WHERE RES.ID IN ( SELECT DISTINCT EIM.ID FROM EAInstitutionMgr EIM LEFT JOIN GSPWFRUWORKITEM GWWI ON EIM.ID = GWWI.BIZINSTID WHERE EIM.BILLSTATE = '2' AND EIM.RULETYPE = '0' AND EIM.ISLATEST = '1' AND GWWI.ACTUALPARTICIPANT = :CL )";

         for(DynamicResultRow item : iBqlExecuter.executeSelectStatement(tempBql, refEntityIDs, new IDbParameter[]{iDbParameters})) {
            Map<String, Object> map = new HashMap();

            for(int i = 0; i < item.getValues().size(); ++i) {
               if (!"".equals(item.get_columnNames().get(i)) && item.get_columnNames().get(i) != null) {
                  map.put(item.get_columnNames().get(i), item.getValues().get(i));
               }
            }

            list.add(map);
         }
      }

      return JSON.toJSONString(list);
   }

   public String getCountByOrgId(String ids) {
      String[] arr = ids.split(",");
      List<Map<String, Object>> list = new ArrayList();
      int cnt = 0;

      for(String arrItem : arr) {
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("7cdc0646-2a4d-474e-be60-338f4af635ca");
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("CL", arrItem);
         String tempBql = "SELECT ECT.NAME AS Category, RES.CODE, EDL.NAME AS DOCLEVEL, RES.KEYWORD, RES.MGRDEPART , RES.NAME, RES.OFFICIALDOCNUM, RES.PUBLISHDATE01 AS PUBLISHDATE, BAO.NAME_CHS AS PUBLISHDEPART , CASE  WHEN RES.RANGE = '0' THEN '集团本部' WHEN RES.RANGE = '1' THEN '下属单位' WHEN RES.RANGE = '2' THEN '集团本部及下属单位' END AS RANGE, RES.REGISTDEPART, RES.RULEVERSION , CASE  WHEN RES.SLAVESYSTEM = '0' THEN '党群类' WHEN RES.SLAVESYSTEM = '1' THEN '行政类' END AS SLAVESYSTEM FROM EAInstitutionMgr RES LEFT JOIN eaCategoryType ECT ON RES.CATEGORY = ECT.ID LEFT JOIN EADocLevel EDL ON RES.DOCLEVEL = EDL.ID LEFT JOIN BFADMINORGANIZATION BAO ON RES.DOCLEVEL = BAO.ID WHERE RES.ID IN ( SELECT DISTINCT ID FROM EAInstitutionMgr WHERE BILLSTATE = '2' AND RULETYPE='0' AND ISLATEST='1' \nAND PUBLISHDEPART = :CL )";
         List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(tempBql, refEntityIDs, new IDbParameter[]{iDbParameters});
         cnt += mainData.size();
      }

      Map<String, Object> map = new HashMap();
      map.put("count", cnt);
      list.add(map);
      return JSON.toJSONString(list);
   }

   public String getCountByUserId(String ids) {
      String[] arr = ids.split(",");
      List<Map<String, Object>> list = new ArrayList();
      int cnt = 0;

      for(String arrItem : arr) {
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("7cdc0646-2a4d-474e-be60-338f4af635ca");
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("CL", arrItem);
         String tempBql = "SELECT ECT.NAME AS Category, RES.CODE, EDL.NAME AS DOCLEVEL, RES.KEYWORD, RES.MGRDEPART , RES.NAME, RES.OFFICIALDOCNUM, RES.PUBLISHDATE01 AS PUBLISHDATE, BAO.NAME_CHS AS PUBLISHDEPART , CASE  WHEN RES.RANGE = '0' THEN '集团本部' WHEN RES.RANGE = '1' THEN '下属单位' WHEN RES.RANGE = '2' THEN '集团本部及下属单位' END AS RANGE, RES.REGISTDEPART, RES.RULEVERSION , CASE  WHEN RES.SLAVESYSTEM = '0' THEN '党群类' WHEN RES.SLAVESYSTEM = '1' THEN '行政类' END AS SLAVESYSTEM FROM EAInstitutionMgr RES LEFT JOIN eaCategoryType ECT ON RES.CATEGORY = ECT.ID LEFT JOIN EADocLevel EDL ON RES.DOCLEVEL = EDL.ID LEFT JOIN BFADMINORGANIZATION BAO ON RES.DOCLEVEL = BAO.ID WHERE RES.ID IN ( SELECT DISTINCT EIM.ID FROM EAInstitutionMgr EIM LEFT JOIN GSPWFRUWORKITEM GWWI ON EIM.ID = GWWI.BIZINSTID WHERE EIM.BILLSTATE = '2' AND EIM.RULETYPE = '0' AND EIM.ISLATEST = '1' AND GWWI.ACTUALPARTICIPANT = :CL )";
         List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(tempBql, refEntityIDs, new IDbParameter[]{iDbParameters});
         cnt += mainData.size();
      }

      Map<String, Object> map = new HashMap();
      map.put("count", cnt);
      list.add(map);
      return JSON.toJSONString(list);
   }
}
