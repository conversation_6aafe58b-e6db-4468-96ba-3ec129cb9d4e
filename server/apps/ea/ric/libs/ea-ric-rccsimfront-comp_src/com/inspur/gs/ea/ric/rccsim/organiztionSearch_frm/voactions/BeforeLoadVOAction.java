package com.inspur.gs.ea.ric.rccsim.organiztionSearch_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class BeforeLoadVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;
   private static final String ONE = "1";

   public BeforeLoadVOAction(QueryContext context) {
      super(context);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      String paramValue = "";
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "EAInstitutionOrg")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultParam = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(resultParam)) {
         paramValue = this.valueOf(((DynamicResultRow)resultParam.get(0)).get("configvalue"));
      }

      if ("1".equals(paramValue)) {
         EntityFilter filter = this.getQueryContext().getFilter();
         ArrayList<FilterCondition> list = new ArrayList();
         FilterCondition filterCondition = new FilterCondition(0, "orgtype", ExpressCompareType.Equal, "9edf25d1-b991-4dde-90b9-30145422d24d", 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         filter.addFilterConditions(list);
      }

   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
