package com.inspur.gs.ea.ric.rccsim.institutionmgr01_frm.voactions;

import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.api.lcp.LcpFactoryManagerUtils;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.common.commonudt.billstate.entity.BillStateEnum;

public class UpdateRuleVersionVOAction extends AbstractFSAction<Boolean> {
   private String id;

   public UpdateRuleVersionVOAction(String id) {
      this.id = id;
   }

   public void execute() {
      ModifyChangeDetail change = new ModifyChangeDetail(this.id);
      ValueObjModifyChangeDetail billStateChange = new ValueObjModifyChangeDetail();
      billStateChange.getPropertyChanges().put("BillState", BillStateEnum.Billing);
      change.getPropertyChanges().put("BillState", billStateChange);
      IStandardLcp lcp = LcpFactoryManagerUtils.getBefLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      lcp.modify(change);
      this.setResult(true);
   }
}
