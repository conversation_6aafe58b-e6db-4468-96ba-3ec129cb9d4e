package com.inspur.gs.ea.ric.rccsim.institutionmgrinlist_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class BeforeSearchVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;

   public BeforeSearchVOAction(QueryContext context) {
      super(context);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      WebSession webSession = CAFContext.current.getSession();
      String DeptId = this.valueOf(this.getContext().getVariableData().getValue("deptIds"));
      String paramValue = "";
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "EARuleQrOrgLevel")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultParam = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(resultParam)) {
         paramValue = this.valueOf(((DynamicResultRow)resultParam.get(0)).get("configvalue"));
      }

      String sysOrgId = webSession.getSysOrgId();
      if (sysOrgId == null || sysOrgId.isEmpty()) {
         String userId = webSession.getUserId();
         IBqlExecuter iBqlExecuter = this.getBqlExecuter();
         iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         String selectSql = "select SYSORGID from GSPUSER where id=:id";
         ArrayList<String> planID = new ArrayList();
         IDbParameter selectPara = iBqlExecuter.makeInParam("id", userId);
         List<DynamicResultRow> result = iBqlExecuter.executeSelectStatement(selectSql, planID, new IDbParameter[]{selectPara});
         if (result.size() > 0) {
            sysOrgId = ((DynamicResultRow)result.get(0)).get("SYSORGID").toString();
         }
      }

      IBqlExecuter iBqlExecuter = this.getBqlExecuter();
      iBqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String queryLevel = "select TREEINFO_PATH from BFADMINORGANIZATION  where id = :id";
      ArrayList<String> levelQuery = new ArrayList();
      IDbParameter paramLevel = iBqlExecuter.makeInParam("id", sysOrgId);
      List<DynamicResultRow> levelResut = iBqlExecuter.executeSelectStatement(queryLevel, levelQuery, new IDbParameter[]{paramLevel});
      String employeeDept = String.valueOf(((DynamicResultRow)levelResut.get(0)).get("TREEINFO_PATH"));
      ArrayList<String> deptlQuery = new ArrayList();
      String dept = "select TREEINFO_PATH from BFADMINORGANIZATION where NAME_CHS =:NAME_CHS";
      IDbParameter paramDept = iBqlExecuter.makeInParam("NAME_CHS", "集团公司本部");
      List<DynamicResultRow> DeptResut = iBqlExecuter.executeSelectStatement(dept, deptlQuery, new IDbParameter[]{paramDept});
      if (CollectionUtils.isEmpty(DeptResut)) {
         ArrayList<String> refEntityIDs = new ArrayList();
         String queryDeptCode = "select TREEINFO_PATH from BFADMINORGANIZATION where id =:id";
         IDbParameter iDbParameters = iBqlExecuter.makeInParam("id", DeptId);
         List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(queryDeptCode, refEntityIDs, new IDbParameter[]{iDbParameters});
         List<DynamicResultRow> deptData = null;
         if (mainData.size() != 0) {
            String queryDepts = "select id from BFADMINORGANIZATION where TREEINFO_PATH like :treeinfo_path";
            IDbParameter paramCode = iBqlExecuter.makeInParam("treeinfo_path", ((DynamicResultRow)mainData.get(0)).get("treeinfo_path") + "%");
            deptData = iBqlExecuter.executeSelectStatement(queryDepts, refEntityIDs, new IDbParameter[]{paramCode});
         }

         String str = "";
         if (deptData != null) {
            for(DynamicResultRow res : deptData) {
               str = str + res.getValues().get(0).toString() + "\r\n";
            }

            str = str.substring(0, str.length() - 2);
         }

         EntityFilter filter = this.getQueryContext().getFilter();
         ArrayList<FilterCondition> list = new ArrayList();
         FilterCondition filterCondition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "In", 0, ExpressRelationType.And, ExpressValueType.Value);
         FilterCondition filterCondition1 = new FilterCondition(0, "billState", ExpressCompareType.Equal, "Approved", 0, ExpressRelationType.And, ExpressValueType.Value);
         FilterCondition filterCondition2 = null;
         FilterCondition filterCondition4 = new FilterCondition(0, "insStatus", ExpressCompareType.Equal, "No", 0, ExpressRelationType.Empty, ExpressValueType.Value);
         if ("0".equals(paramValue)) {
            filterCondition2 = new FilterCondition(1, "mgrDepart", ExpressCompareType.In, str, 1, ExpressRelationType.And, ExpressValueType.Value);
         } else {
            filterCondition2 = new FilterCondition(0, "mgrDepart", ExpressCompareType.Equal, DeptId, 0, ExpressRelationType.And, ExpressValueType.Value);
         }

         list.add(filterCondition);
         list.add(filterCondition1);
         list.add(filterCondition2);
         list.add(filterCondition4);
         filter.addFilterConditions(list);
      } else {
         String deptCode = String.valueOf(((DynamicResultRow)DeptResut.get(0)).get("TREEINFO_PATH"));
         if (levelResut.size() != 0) {
            if (employeeDept.length() >= deptCode.length() && !employeeDept.substring(0, deptCode.length()).equals(deptCode)) {
               EntityFilter filters = this.getQueryContext().getFilter();
               ArrayList<FilterCondition> list = new ArrayList();
               ArrayList<String> refEntityIDs = new ArrayList();
               String queryDeptCode = "select TREEINFO_PATH from BFADMINORGANIZATION where id =:id";
               IDbParameter iDbParameters = iBqlExecuter.makeInParam("id", DeptId);
               List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(queryDeptCode, refEntityIDs, new IDbParameter[]{iDbParameters});
               List<DynamicResultRow> deptData = null;
               if (mainData.size() != 0) {
                  String queryDepts = "select id from BFADMINORGANIZATION where TREEINFO_PATH like :treeinfo_path";
                  IDbParameter paramCode = iBqlExecuter.makeInParam("treeinfo_path", ((DynamicResultRow)mainData.get(0)).get("treeinfo_path") + "%");
                  deptData = iBqlExecuter.executeSelectStatement(queryDepts, refEntityIDs, new IDbParameter[]{paramCode});
               }

               String str = "";
               if (deptData != null) {
                  for(DynamicResultRow res : deptData) {
                     str = str + res.getValues().get(0).toString() + "\r\n";
                  }

                  str = str.substring(0, str.length() - 2);
               }

               FilterCondition filterCondition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "In", 0, ExpressRelationType.And, ExpressValueType.Value);
               FilterCondition filterCondition1 = new FilterCondition(0, "billState", ExpressCompareType.Equal, "Approved", 0, ExpressRelationType.And, ExpressValueType.Value);
               FilterCondition filterCondition4 = null;
               FilterCondition filterCondition6 = new FilterCondition(0, "insStatus", ExpressCompareType.Equal, "No", 0, ExpressRelationType.Empty, ExpressValueType.Value);
               if ("0".equals(paramValue)) {
                  filterCondition4 = new FilterCondition(1, "mgrDepart", ExpressCompareType.In, str, 1, ExpressRelationType.And, ExpressValueType.Value);
               } else {
                  filterCondition4 = new FilterCondition(0, "mgrDepart", ExpressCompareType.Equal, DeptId, 0, ExpressRelationType.And, ExpressValueType.Value);
               }

               list.add(filterCondition);
               list.add(filterCondition1);
               list.add(filterCondition4);
               list.add(filterCondition6);
               filters.addFilterConditions(list);
            } else {
               ArrayList<String> refEntityIDs = new ArrayList();
               String queryDeptCode = "select TREEINFO_PATH from BFADMINORGANIZATION where id =:id";
               IDbParameter iDbParameters = iBqlExecuter.makeInParam("id", DeptId);
               List<DynamicResultRow> mainData = iBqlExecuter.executeSelectStatement(queryDeptCode, refEntityIDs, new IDbParameter[]{iDbParameters});
               List<DynamicResultRow> deptData = null;
               if (mainData.size() != 0) {
                  String queryDepts = "select id from BFADMINORGANIZATION where TREEINFO_PATH like :treeinfo_path";
                  IDbParameter paramCode = iBqlExecuter.makeInParam("treeinfo_path", ((DynamicResultRow)mainData.get(0)).get("treeinfo_path") + "%");
                  deptData = iBqlExecuter.executeSelectStatement(queryDepts, refEntityIDs, new IDbParameter[]{paramCode});
               }

               String str = "";
               if (deptData != null) {
                  for(DynamicResultRow res : deptData) {
                     str = str + res.getValues().get(0).toString() + "\r\n";
                  }

                  str = str.substring(0, str.length() - 2);
               }

               EntityFilter filter = this.getQueryContext().getFilter();
               ArrayList<FilterCondition> list = new ArrayList();
               FilterCondition filterCondition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "In", 0, ExpressRelationType.And, ExpressValueType.Value);
               FilterCondition filterCondition1 = new FilterCondition(0, "billState", ExpressCompareType.Equal, "Approved", 0, ExpressRelationType.And, ExpressValueType.Value);
               FilterCondition filterCondition2 = null;
               FilterCondition filterCondition4 = new FilterCondition(0, "insStatus", ExpressCompareType.Equal, "No", 0, ExpressRelationType.Empty, ExpressValueType.Value);
               if ("0".equals(paramValue)) {
                  filterCondition2 = new FilterCondition(1, "mgrDepart", ExpressCompareType.In, str, 1, ExpressRelationType.And, ExpressValueType.Value);
               } else {
                  filterCondition2 = new FilterCondition(0, "mgrDepart", ExpressCompareType.Equal, DeptId, 0, ExpressRelationType.And, ExpressValueType.Value);
               }

               list.add(filterCondition);
               list.add(filterCondition1);
               list.add(filterCondition2);
               list.add(filterCondition4);
               filter.addFilterConditions(list);
            }
         }

      }
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
