package com.inspur.gs.ea.ric.rccsim.listenerTask;

import com.inspur.edp.wf.runtime.entity.WorkItemState;
import com.inspur.edp.wf.runtime.entity.WorkItemType;
import java.util.Date;

public interface WorkItem {
   String getId();

   String getName();

   WorkItemType getWorkItemType();

   String getRefWorkItemId();

   String getActivityInstanceId();

   String getActivityDefinitionId();

   String getProcessInstanceId();

   String getSuperProcessInstanceId();

   String getBizActivityId();

   String getBizDefKey();

   String getBizInstId();

   String getProcessDefinitionId();

   WorkItemState getState();

   int getOrdinal();

   int getPriority();

   Date getStartedTime();

   Date getDueTime();

   String getSubmitter();

   String getParticipant();

   String getParticipantName();

   String getOwner();

   String getOwnerName();

   String getDescription();

   boolean getReadState();

   Date getReadTime();

   Date getClaimTime();

   int getUrgeTimes();

   String getActualParticipantName();

   String getActualParticipant();

   Date getCompletedTime();

   int getExecutionDuration();

   int getOverdueDuration();

   Date getCreatedTime();

   String getCreator();

   Date getLastModifiedTime();

   String getLastModifier();
}
