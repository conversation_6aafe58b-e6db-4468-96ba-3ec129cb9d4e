package com.inspur.gs.ea.ric.rccsim.institutionmgr_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class toAbolishVOAction extends AbstractFSAction<String> {
   private String id;
   private IBqlExecuter bqlExecuter;

   public toAbolishVOAction(String id) {
      this.id = id;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("7cdc0646-2a4d-474e-be60-338f4af635ca");
      String bqlStatement2 = "UPDATE EAINSTITUTIONMGR set ISLATEST = '0' where id = :id";
      IDbParameter iDbParameters2 = bqlExecuter.makeInParam("id", this.id);
      List<DynamicResultRow> result = null;
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      bqlExecuter.executeBqlStatement(bqlStatement2, refEntityIDs, new IDbParameter[]{iDbParameters2});
      this.setResult("操作成功。");
   }
}
