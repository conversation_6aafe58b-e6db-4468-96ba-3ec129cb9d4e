package com.inspur.gs.ea.ric.rccsim.institutionmgrincard_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class EditStateVOAction extends AbstractFSAction<Boolean> {
   private String id;
   private IBqlExecuter bqlExecuter;

   public EditStateVOAction(String id) {
      this.id = id;
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      WebSession webSession = CAFContext.current.getSession();
      String userName = webSession.getUserName();
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> edit = new ArrayList();
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      IDbParameter iDbParameters = bqlExecuter.makeInParam("id", this.id);
      String sql = "select updateInfo_CreatedBy from EAINSTITUTIONMGR where id =:id";
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(sql, edit, new IDbParameter[]{iDbParameters});
      boolean flag = false;
      if (userName.equals(((DynamicResultRow)resultCheckOrgType.get(0)).getValues().toString())) {
         flag = true;
      }

      if (userName.equals(((DynamicResultRow)resultCheckOrgType.get(0)).get("updateInfo_CreatedBy").toString())) {
         flag = true;
      }

      this.setResult(flag);
   }
}
