package com.inspur.gs.ea.ric.rccsim.institutionaltotallist_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import java.util.ArrayList;

public class BeforeQueryVOAction extends BeforeQueryAction {
   public BeforeQueryVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      FilterCondition filterCondition = new FilterCondition(0, "type", ExpressCompareType.Equal, "In", 0, ExpressRelationType.Empty, ExpressValueType.Value);
      list.add(filterCondition);
      filter.addFilterConditions(list);
   }
}
