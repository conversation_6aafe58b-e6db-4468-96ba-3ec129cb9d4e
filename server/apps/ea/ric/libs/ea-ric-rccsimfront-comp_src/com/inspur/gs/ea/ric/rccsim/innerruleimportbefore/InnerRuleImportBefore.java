package com.inspur.gs.ea.ric.rccsim.innerruleimportbefore;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.edp.dxc.entity.data.CommonDataHeader;
import com.inspur.edp.dxc.entity.data.DxcCommonData;
import com.inspur.edp.dxc.spi.components.BaseCustomValidateComponent;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Generated;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@GspComponent("c154c33a-6726-4ddb-8dca-979d9218629e")
public class InnerRuleImportBefore extends BaseCustomValidateComponent {
   @Generated
   private static final Logger log = LoggerFactory.getLogger(InnerRuleImportBefore.class);
   private IBqlExecuter bqlExecuter;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void importBefore() {
      DxcCommonData dxcCommonData = super.getData();
      CommonDataHeader dataHeader = dxcCommonData.getCommonDataHeader();
      String globalParam = super.getGlobalParam();
      List<DynamicResultRow> codeList = this.getCodeList();

      for(int i = 0; i < dxcCommonData.getDataSet().size(); ++i) {
         if (CollectionUtils.isNotEmpty(codeList)) {
            for(int j = 0; j < codeList.size(); ++j) {
               if (((DynamicResultRow)codeList.get(j)).get("CODE").equals(dxcCommonData.getDataByCode(i, "Code"))) {
                  super.addValidateInfo("格式校验", "内部制度管理", String.format("第%d行%s已存在", i + 1, dataHeader.getFieldNameByCode("Code")));
               }
            }
         }

         String publishDate = dxcCommonData.getDataByCode(i, "PublishDate01").toString();
         if (!isLegalDate(publishDate, publishDate.length(), "yyyy-MM-dd")) {
            super.addValidateInfo("格式校验", "内部制度管理", String.format("第%d行%s不符合yyyy-MM-dd日期格式", i + 1, dataHeader.getFieldNameByCode("PublishDate01")));
         }
      }

   }

   private List<DynamicResultRow> getCodeList() {
      this.getBqlExecuter();
      ArrayList<String> planIDs = new ArrayList();
      String sqlSelectDept = "select CODE from EAInstitutionMgr WHERE RuleType = '0'";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> list = this.bqlExecuter.executeSelectStatement(sqlSelectDept, planIDs, new IDbParameter[0]);
      return list;
   }

   public static boolean isLegalDate(String date, int length, String format) {
      if (date != null && date.length() == length) {
         try {
            DateFormat formatter = new SimpleDateFormat(format);
            formatter.setLenient(false);
            Date date1 = formatter.parse(date);
            return date.equals(formatter.format(date1));
         } catch (Exception e) {
            log.info(e.getMessage(), e);
            return false;
         }
      } else {
         return false;
      }
   }
}
