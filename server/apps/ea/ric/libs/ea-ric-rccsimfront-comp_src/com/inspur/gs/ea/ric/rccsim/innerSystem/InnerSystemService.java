package com.inspur.gs.ea.ric.rccsim.innerSystem;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;

@Path("/innerInstitute")
@Consumes({"application/json"})
@Produces({"application/json"})
public interface InnerSystemService {
   @GET
   @Path("/getDataByOrgId/{ids}")
   String getDataByOrgId(@PathParam("ids") String var1);

   @GET
   @Path("/getDataByUserId/{ids}")
   String getDataByUserId(@PathParam("ids") String var1);

   @GET
   @Path("/getCountByOrgId/{ids}")
   String getCountByOrgId(@PathParam("ids") String var1);

   @GET
   @Path("/getCountByUserId/{ids}")
   String getCountByUserId(@PathParam("ids") String var1);
}
