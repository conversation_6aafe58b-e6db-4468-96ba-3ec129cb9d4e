package com.inspur.gs.ea.ric.rccsim.listenerTask;

import com.inspur.edp.wf.runtime.entity.ActivityInstance;
import com.inspur.edp.wf.runtime.entity.ProcessInstance;
import java.util.HashMap;
import java.util.Map;
import lombok.Generated;

public class WorkContext {
   private String processInstanceId;
   private String dataId;
   private String bizDefKey;
   private String processDefinitionId;
   private String processDefinitionKey;
   private ProcessInstance currentProcessInstance;
   private ActivityInstance currentActivityInstance;
   private com.inspur.edp.wf.runtime.entity.WorkItem currentWorkItem;
   private Map<String, Object> variables;
   private Map<String, Object> schemaVariables;

   public void WorkflowContext() {
      this.setVariables(new HashMap());
      this.setSchemaVariables(new HashMap());
   }

   @Generated
   public String getProcessInstanceId() {
      return this.processInstanceId;
   }

   @Generated
   public String getDataId() {
      return this.dataId;
   }

   @Generated
   public String getBizDefKey() {
      return this.bizDefKey;
   }

   @Generated
   public String getProcessDefinitionId() {
      return this.processDefinitionId;
   }

   @Generated
   public String getProcessDefinitionKey() {
      return this.processDefinitionKey;
   }

   @Generated
   public ProcessInstance getCurrentProcessInstance() {
      return this.currentProcessInstance;
   }

   @Generated
   public ActivityInstance getCurrentActivityInstance() {
      return this.currentActivityInstance;
   }

   @Generated
   public com.inspur.edp.wf.runtime.entity.WorkItem getCurrentWorkItem() {
      return this.currentWorkItem;
   }

   @Generated
   public Map<String, Object> getVariables() {
      return this.variables;
   }

   @Generated
   public Map<String, Object> getSchemaVariables() {
      return this.schemaVariables;
   }

   @Generated
   public void setProcessInstanceId(String processInstanceId) {
      this.processInstanceId = processInstanceId;
   }

   @Generated
   public void setDataId(String dataId) {
      this.dataId = dataId;
   }

   @Generated
   public void setBizDefKey(String bizDefKey) {
      this.bizDefKey = bizDefKey;
   }

   @Generated
   public void setProcessDefinitionId(String processDefinitionId) {
      this.processDefinitionId = processDefinitionId;
   }

   @Generated
   public void setProcessDefinitionKey(String processDefinitionKey) {
      this.processDefinitionKey = processDefinitionKey;
   }

   @Generated
   public void setCurrentProcessInstance(ProcessInstance currentProcessInstance) {
      this.currentProcessInstance = currentProcessInstance;
   }

   @Generated
   public void setCurrentActivityInstance(ActivityInstance currentActivityInstance) {
      this.currentActivityInstance = currentActivityInstance;
   }

   @Generated
   public void setCurrentWorkItem(com.inspur.edp.wf.runtime.entity.WorkItem currentWorkItem) {
      this.currentWorkItem = currentWorkItem;
   }

   @Generated
   public void setVariables(Map<String, Object> variables) {
      this.variables = variables;
   }

   @Generated
   public void setSchemaVariables(Map<String, Object> schemaVariables) {
      this.schemaVariables = schemaVariables;
   }

   @Generated
   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof WorkContext)) {
         return false;
      } else {
         WorkContext other = (WorkContext)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$processInstanceId = this.getProcessInstanceId();
            Object other$processInstanceId = other.getProcessInstanceId();
            if (this$processInstanceId == null) {
               if (other$processInstanceId != null) {
                  return false;
               }
            } else if (!this$processInstanceId.equals(other$processInstanceId)) {
               return false;
            }

            Object this$dataId = this.getDataId();
            Object other$dataId = other.getDataId();
            if (this$dataId == null) {
               if (other$dataId != null) {
                  return false;
               }
            } else if (!this$dataId.equals(other$dataId)) {
               return false;
            }

            Object this$bizDefKey = this.getBizDefKey();
            Object other$bizDefKey = other.getBizDefKey();
            if (this$bizDefKey == null) {
               if (other$bizDefKey != null) {
                  return false;
               }
            } else if (!this$bizDefKey.equals(other$bizDefKey)) {
               return false;
            }

            Object this$processDefinitionId = this.getProcessDefinitionId();
            Object other$processDefinitionId = other.getProcessDefinitionId();
            if (this$processDefinitionId == null) {
               if (other$processDefinitionId != null) {
                  return false;
               }
            } else if (!this$processDefinitionId.equals(other$processDefinitionId)) {
               return false;
            }

            Object this$processDefinitionKey = this.getProcessDefinitionKey();
            Object other$processDefinitionKey = other.getProcessDefinitionKey();
            if (this$processDefinitionKey == null) {
               if (other$processDefinitionKey != null) {
                  return false;
               }
            } else if (!this$processDefinitionKey.equals(other$processDefinitionKey)) {
               return false;
            }

            Object this$currentProcessInstance = this.getCurrentProcessInstance();
            Object other$currentProcessInstance = other.getCurrentProcessInstance();
            if (this$currentProcessInstance == null) {
               if (other$currentProcessInstance != null) {
                  return false;
               }
            } else if (!this$currentProcessInstance.equals(other$currentProcessInstance)) {
               return false;
            }

            Object this$currentActivityInstance = this.getCurrentActivityInstance();
            Object other$currentActivityInstance = other.getCurrentActivityInstance();
            if (this$currentActivityInstance == null) {
               if (other$currentActivityInstance != null) {
                  return false;
               }
            } else if (!this$currentActivityInstance.equals(other$currentActivityInstance)) {
               return false;
            }

            Object this$currentWorkItem = this.getCurrentWorkItem();
            Object other$currentWorkItem = other.getCurrentWorkItem();
            if (this$currentWorkItem == null) {
               if (other$currentWorkItem != null) {
                  return false;
               }
            } else if (!this$currentWorkItem.equals(other$currentWorkItem)) {
               return false;
            }

            Object this$variables = this.getVariables();
            Object other$variables = other.getVariables();
            if (this$variables == null) {
               if (other$variables != null) {
                  return false;
               }
            } else if (!this$variables.equals(other$variables)) {
               return false;
            }

            Object this$schemaVariables = this.getSchemaVariables();
            Object other$schemaVariables = other.getSchemaVariables();
            if (this$schemaVariables == null) {
               if (other$schemaVariables != null) {
                  return false;
               }
            } else if (!this$schemaVariables.equals(other$schemaVariables)) {
               return false;
            }

            return true;
         }
      }
   }

   @Generated
   protected boolean canEqual(Object other) {
      return other instanceof WorkContext;
   }

   @Generated
   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $processInstanceId = this.getProcessInstanceId();
      result = result * 59 + ($processInstanceId == null ? 43 : $processInstanceId.hashCode());
      Object $dataId = this.getDataId();
      result = result * 59 + ($dataId == null ? 43 : $dataId.hashCode());
      Object $bizDefKey = this.getBizDefKey();
      result = result * 59 + ($bizDefKey == null ? 43 : $bizDefKey.hashCode());
      Object $processDefinitionId = this.getProcessDefinitionId();
      result = result * 59 + ($processDefinitionId == null ? 43 : $processDefinitionId.hashCode());
      Object $processDefinitionKey = this.getProcessDefinitionKey();
      result = result * 59 + ($processDefinitionKey == null ? 43 : $processDefinitionKey.hashCode());
      Object $currentProcessInstance = this.getCurrentProcessInstance();
      result = result * 59 + ($currentProcessInstance == null ? 43 : $currentProcessInstance.hashCode());
      Object $currentActivityInstance = this.getCurrentActivityInstance();
      result = result * 59 + ($currentActivityInstance == null ? 43 : $currentActivityInstance.hashCode());
      Object $currentWorkItem = this.getCurrentWorkItem();
      result = result * 59 + ($currentWorkItem == null ? 43 : $currentWorkItem.hashCode());
      Object $variables = this.getVariables();
      result = result * 59 + ($variables == null ? 43 : $variables.hashCode());
      Object $schemaVariables = this.getSchemaVariables();
      result = result * 59 + ($schemaVariables == null ? 43 : $schemaVariables.hashCode());
      return result;
   }

   @Generated
   public String toString() {
      return "WorkContext(processInstanceId=" + this.getProcessInstanceId() + ", dataId=" + this.getDataId() + ", bizDefKey=" + this.getBizDefKey() + ", processDefinitionId=" + this.getProcessDefinitionId() + ", processDefinitionKey=" + this.getProcessDefinitionKey() + ", currentProcessInstance=" + this.getCurrentProcessInstance() + ", currentActivityInstance=" + this.getCurrentActivityInstance() + ", currentWorkItem=" + this.getCurrentWorkItem() + ", variables=" + this.getVariables() + ", schemaVariables=" + this.getSchemaVariables() + ")";
   }
}
