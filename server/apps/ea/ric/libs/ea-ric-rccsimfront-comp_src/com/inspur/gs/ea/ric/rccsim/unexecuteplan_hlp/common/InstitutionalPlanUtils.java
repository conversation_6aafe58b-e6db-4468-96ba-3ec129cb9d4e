package com.inspur.gs.ea.ric.rccsim.unexecuteplan_hlp.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import java.util.Date;

public final class InstitutionalPlanUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static IBillState getBillStatus(IEntityData data) {
      return (IBillState)EntityDataUtils.getValue(data, "BillStatus");
   }

   public static void setBillStatus(IEntityData data, IBillState propertyValue) {
      EntityDataUtils.setValue(data, "BillStatus", propertyValue);
   }

   public static IProcessInstance getProcessInstance(IEntityData data) {
      return (IProcessInstance)EntityDataUtils.getValue(data, "ProcessInstance");
   }

   public static void setProcessInstance(IEntityData data, IProcessInstance propertyValue) {
      EntityDataUtils.setValue(data, "ProcessInstance", propertyValue);
   }

   public static AssoInfoBase getAgent(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Agent");
   }

   public static void setAgent(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Agent", propertyValue);
   }

   public static String getOfficialDocNum(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "OfficialDocNum");
   }

   public static void setOfficialDocNum(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "OfficialDocNum", propertyValue);
   }

   public static String getDocName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "DocName");
   }

   public static void setDocName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "DocName", propertyValue);
   }

   public static Date getPreFinishTime(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "PreFinishTime");
   }

   public static void setPreFinishTime(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "PreFinishTime", propertyValue);
   }

   public static String getIsExecution(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "IsExecution");
   }

   public static void setIsExecution(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "IsExecution", propertyValue);
   }

   public static Date getFinishTime(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "FinishTime");
   }

   public static void setFinishTime(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "FinishTime", propertyValue);
   }

   public static AssoInfoBase getDutyDept(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "DutyDept");
   }

   public static void setDutyDept(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "DutyDept", propertyValue);
   }

   public static String getExecuteWay(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ExecuteWay");
   }

   public static void setExecuteWay(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ExecuteWay", propertyValue);
   }

   public static String getRemark(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Remark");
   }

   public static void setRemark(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Remark", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static String getEXT6(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT6");
   }

   public static void setEXT6(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT6", propertyValue);
   }

   public static String getReason(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Reason");
   }

   public static void setReason(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Reason", propertyValue);
   }

   public static Integer getYear(IEntityData data) {
      return (Integer)EntityDataUtils.getValue(data, "Year");
   }

   public static void setYear(IEntityData data, Integer propertyValue) {
      EntityDataUtils.setValue(data, "Year", propertyValue);
   }

   public static String getYearStr(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "YearStr");
   }

   public static void setYearStr(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "YearStr", propertyValue);
   }
}
