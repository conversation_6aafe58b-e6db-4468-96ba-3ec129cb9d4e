package com.inspur.gs.ea.ric.rccsim.institutionmgr01_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.CafSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class beforeSearchVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public beforeSearchVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      String paramValue = "";
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "EARuleMgrQueryType")};
      String sqlselect = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultParam = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(resultParam)) {
         paramValue = this.valueOf(((DynamicResultRow)resultParam.get(0)).get("configvalue"));
      }

      CafSession webSession = CAFContext.current.getCurrentSession();
      String userName = webSession.getUserName();
      String userId = webSession.getUserId();
      String orgId = "";
      IDbParameter[] queryOrg = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("userId", userId)};
      String sqlOrg = "select sysorgid from gspuser where id = :userId ";
      List<DynamicResultRow> resultOrg = this.bqlExecuter.executeSelectStatement(sqlOrg, planIDs, queryOrg);
      if (CollectionUtils.isNotEmpty(resultOrg)) {
         orgId = this.valueOf(((DynamicResultRow)resultOrg.get(0)).get("sysorgid"));
      }

      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      FilterCondition filterCondition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "In", 0, ExpressRelationType.And, ExpressValueType.Value);
      FilterCondition filterCondition1 = new FilterCondition(1, "isLatest", ExpressCompareType.Equal, "S", 0, ExpressRelationType.Or, ExpressValueType.Value);
      FilterCondition filterCondition7 = new FilterCondition(1, "isLatest", ExpressCompareType.Equal, "F", 0, ExpressRelationType.And, ExpressValueType.Value);
      FilterCondition filterCondition8 = new FilterCondition(0, "BillState", ExpressCompareType.NotEqual, "Approved", 2, ExpressRelationType.And, ExpressValueType.Value);
      FilterCondition filterCondition2 = new FilterCondition(1, "BillState", ExpressCompareType.Equal, "Billing", 0, ExpressRelationType.Or, ExpressValueType.Value);
      FilterCondition filterCondition3 = new FilterCondition(0, "BillState", ExpressCompareType.Equal, "SubmitApproval", 0, ExpressRelationType.Or, ExpressValueType.Value);
      FilterCondition filterCondition4 = new FilterCondition(0, "BillState", ExpressCompareType.Equal, "Approved", 0, ExpressRelationType.Or, ExpressValueType.Value);
      FilterCondition filterCondition5 = new FilterCondition(0, "BillState", ExpressCompareType.Equal, "ApprovalNotPassed", 1, ExpressRelationType.And, ExpressValueType.Value);
      FilterCondition filterCondition6 = null;
      if ("0".equals(paramValue)) {
         filterCondition6 = new FilterCondition(0, "UpdateInfo_CreatedBy", ExpressCompareType.Equal, userName, 0, ExpressRelationType.Empty, ExpressValueType.Value);
      } else if ("1".equals(paramValue)) {
         filterCondition6 = new FilterCondition(0, "MgrDepart", ExpressCompareType.Equal, orgId, 0, ExpressRelationType.Empty, ExpressValueType.Value);
      } else {
         filterCondition5 = new FilterCondition(0, "BillState", ExpressCompareType.Equal, "ApprovalNotPassed", 1, ExpressRelationType.Empty, ExpressValueType.Value);
      }

      list.add(filterCondition);
      list.add(filterCondition1);
      list.add(filterCondition7);
      list.add(filterCondition8);
      list.add(filterCondition2);
      list.add(filterCondition3);
      list.add(filterCondition4);
      list.add(filterCondition5);
      if (!"2".equals(paramValue)) {
         list.add(filterCondition6);
      }

      filter.addFilterConditions(list);
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
