package com.inspur.gs.ea.ric.rccsim.unexecuteplan_hlp.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

public class BeforeQueryVOAction extends BeforeQueryAction {
   public BeforeQueryVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      Date date = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
      String year = sdf.format(date);
      FilterCondition filterCondition = new FilterCondition(0, "yearStr", ExpressCompareType.Equal, year, 0, ExpressRelationType.Empty, ExpressValueType.Value);
      list.add(filterCondition);
      filter.addFilterConditions(list);
   }
}
