package com.inspur.gs.ea.ric.rccsim.institutionmgr_frm.voactions;

import com.inspur.edp.bff.spi.AbstractHelpAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.web.help.api.LookupQueryParam;
import java.util.ArrayList;

public class SystemMergeHelpBeforeActionVOAction extends AbstractHelpAction {
   public void beforeHelp(LookupQueryParam lookupQueryParam) {
      EntityFilter filter = new EntityFilter();
      FilterCondition condition = new FilterCondition(0, "ruleType", ExpressCompareType.Equal, "Out", 0, ExpressRelationType.Empty, ExpressValueType.Value);
      ArrayList<FilterCondition> list = new ArrayList();
      list.add(condition);
      filter.addFilterConditions(list);
      lookupQueryParam.setFilter(filter);
   }
}
