package com.inspur.gs.ea.ric.rccsim.templatelist_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.permission.api.manager.runtime.PermissionManager;

public class GetOperationVOAction extends AbstractFSAction<Boolean> {
   public void execute() {
      PermissionManager permissionManager = (PermissionManager)SpringBeanUtils.getBean(PermissionManager.class);
      Boolean flag = permissionManager.isPrincipalHasOp("e9a1ca33-3261-358e-de16-074c2c675462");
      this.setResult(flag);
   }
}
