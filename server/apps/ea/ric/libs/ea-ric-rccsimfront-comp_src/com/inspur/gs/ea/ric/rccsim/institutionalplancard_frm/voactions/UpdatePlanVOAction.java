package com.inspur.gs.ea.ric.rccsim.institutionalplancard_frm.voactions;

import com.inspur.edp.bef.api.lcp.ILcpFactory;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.common.commonudt.billstate.entity.BillStateEnum;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;

public class UpdatePlanVOAction extends AbstractFSAction<Boolean> {
   private String id;

   public UpdatePlanVOAction(String id) {
      this.id = id;
   }

   public void execute() {
      ModifyChangeDetail change = new ModifyChangeDetail(this.id);
      ValueObjModifyChangeDetail billStateChange = new ValueObjModifyChangeDetail();
      billStateChange.getPropertyChanges().put("BillState", BillStateEnum.Billing);
      change.getPropertyChanges().put("BillStatus", billStateChange);
      ValueObjModifyChangeDetail billProcess = new ValueObjModifyChangeDetail();
      billProcess.getPropertyChanges().put("ProcessInstance", "");
      change.getPropertyChanges().put("ProcessInstance", billProcess);
      IStandardLcp lcp = ((ILcpFactory)SpringBeanUtils.getBean(ILcpFactory.class)).createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionalPlan");
      lcp.modify(change);
      this.setResult(true);
   }
}
