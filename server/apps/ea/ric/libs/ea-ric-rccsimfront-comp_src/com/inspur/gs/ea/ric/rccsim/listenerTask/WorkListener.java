package com.inspur.gs.ea.ric.rccsim.listenerTask;

import io.iec.edp.caf.commons.event.IEventListener;

public interface WorkListener extends IEventListener {
   void workItemCreatedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemCompletedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemSuspendedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemResumedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemAbortedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemRetrievedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemClaimedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);

   void workItemUnclaimedEvent(com.inspur.edp.wf.engine.event.WorkItemEventArgs var1);
}
