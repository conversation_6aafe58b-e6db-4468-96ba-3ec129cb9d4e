package com.inspur.gs.ea.ric.rccsim.institutionmgr.common;

import com.inspur.edp.bef.api.BefRtBeanUtil;
import com.inspur.edp.bef.api.be.MgrActionUtils;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.component.attachment.AttachInfo;

public final class InstitutionMgrMgrActionUtils {
   public static void UpdateProcInstIdAndState(String bizInstID, String processInstanceID, String submitOrCancel) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      MgrActionUtils.executeCustomAction(lcp, "UpdateProcInstIdAndState", new Object[]{bizInstID, processInstanceID, submitOrCancel});
   }

   public static Boolean UpdateVersion(String id) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      return (Boolean)MgrActionUtils.executeCustomAction(lcp, "UpdateVersion", new Object[]{id});
   }

   public static Boolean SaveRuleVersion(String id) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      return (Boolean)MgrActionUtils.executeCustomAction(lcp, "SaveRuleVersion", new Object[]{id});
   }

   public static IEntityData BatchUploadAttachment(AttachInfo batchUploadInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "BatchUploadAttachment", new Object[]{batchUploadInfo});
   }

   public static IEntityData UpdateAttachment(AttachInfo updateAttachInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "UpdateAttachment", new Object[]{updateAttachInfo});
   }

   public static Boolean Remove(String DataID) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionMgr");
      return (Boolean)MgrActionUtils.executeCustomAction(lcp, "Remove", new Object[]{DataID});
   }
}
