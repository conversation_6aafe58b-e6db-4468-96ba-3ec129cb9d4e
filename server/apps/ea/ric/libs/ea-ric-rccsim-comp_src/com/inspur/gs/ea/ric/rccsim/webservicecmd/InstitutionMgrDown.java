package com.inspur.gs.ea.ric.rccsim.webservicecmd;

import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

@GspServiceBundle(
   applicationName = "ea",
   serviceUnitName = "ric",
   serviceName = "ric_institutionmgrservice"
)
public interface InstitutionMgrDown {
   @RpcServiceMethod(
      serviceId = "com.inspur.gs.ea.ric.rccsim.webservicecmd.InstitutionMgrDown.institutionMgrDownChange"
   )
   void institutionMgrDownChange(@RpcParam(paramName = "flag") String var1, @RpcParam(paramName = "ID") String var2, @RpcParam(paramName = "categoryId") String var3, @RpcParam(paramName = "docId") String var4);

   @RpcServiceMethod(
      serviceId = "com.inspur.gs.ea.ric.rccsim.webservicecmd.InstitutionMgrDown.updateInstitutionPlan"
   )
   void updateInstitutionPlan(@RpcParam(paramName = "flag") String var1, @RpcParam(paramName = "ID") String var2, @RpcParam(paramName = "planId") String var3);
}
