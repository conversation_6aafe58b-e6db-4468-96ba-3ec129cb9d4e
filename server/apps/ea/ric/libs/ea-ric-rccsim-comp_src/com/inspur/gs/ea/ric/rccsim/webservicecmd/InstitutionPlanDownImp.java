package com.inspur.gs.ea.ric.rccsim.webservicecmd;

import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import org.springframework.util.StringUtils;

public class InstitutionPlanDownImp implements InstitutionPlanDown {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void institutionPlanDownChange(String flag, String ID, String PlanID, String Reason) {
      if ("Pass".equals(flag) && !StringUtils.isEmpty(PlanID)) {
         ArrayList<String> planIDs = new ArrayList();
         IDbParameter[] updateParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", PlanID), this.getBqlExecuter().makeInOutParam("Reason", Reason)};
         String updateSql = "update EAInstitutionalPlan set Reason = :Reason where ID = :ID";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeBqlStatement(updateSql, planIDs, updateParams);
      }

   }
}
