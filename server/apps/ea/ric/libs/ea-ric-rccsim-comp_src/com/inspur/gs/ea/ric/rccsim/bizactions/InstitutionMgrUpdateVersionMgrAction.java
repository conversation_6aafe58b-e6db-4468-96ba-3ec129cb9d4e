package com.inspur.gs.ea.ric.rccsim.bizactions;

import com.inspur.edp.bef.api.be.IBEManagerContext;
import com.inspur.edp.bef.api.be.IBEService;
import com.inspur.edp.bef.api.be.IBusinessEntity;
import com.inspur.edp.bef.spi.action.AbstractManagerAction;

public class InstitutionMgrUpdateVersionMgrAction extends AbstractManagerAction<Boolean> {
   private String id;

   public InstitutionMgrUpdateVersionMgrAction(IBEManagerContext managerContext, String id) {
      super(managerContext);
      this.id = id;
   }

   public void execute() {
      IBusinessEntity entity = this.getEntity(this.id);
      entity.retrieve();
      entity.executeBizAction("UpdateVersion", new Object[0]);
      this.setResult(true);
   }

   private IBusinessEntity getEntity(String dataId) {
      return super.getBEManagerContext().getEntity(dataId);
   }

   private IBEService getMgr() {
      return super.getBEManagerContext().getBEManager();
   }
}
