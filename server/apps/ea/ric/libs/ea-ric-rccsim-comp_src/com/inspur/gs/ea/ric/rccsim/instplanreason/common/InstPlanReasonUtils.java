package com.inspur.gs.ea.ric.rccsim.instplanreason.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import java.util.Date;

public final class InstPlanReasonUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static IBillState getBillStatus(IEntityData data) {
      return (IBillState)EntityDataUtils.getValue(data, "BillStatus");
   }

   public static void setBillStatus(IEntityData data, IBillState propertyValue) {
      EntityDataUtils.setValue(data, "BillStatus", propertyValue);
   }

   public static IProcessInstance getProcessInstance(IEntityData data) {
      return (IProcessInstance)EntityDataUtils.getValue(data, "ProcessInstance");
   }

   public static void setProcessInstance(IEntityData data, IProcessInstance propertyValue) {
      EntityDataUtils.setValue(data, "ProcessInstance", propertyValue);
   }

   public static AssoInfoBase getPlanID(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "PlanID");
   }

   public static void setPlanID(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "PlanID", propertyValue);
   }

   public static String getReason(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Reason");
   }

   public static void setReason(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Reason", propertyValue);
   }

   public static String getRemark(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Remark");
   }

   public static void setRemark(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Remark", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static String getEXT6(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT6");
   }

   public static void setEXT6(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT6", propertyValue);
   }

   public static IEntityDataCollection getInstPlanReasonFiles(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "InstPlanReasonFile");
   }
}
