package com.inspur.gs.ea.ric.rccsim.institutionalplan.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.api.manager.ICefValueObjManager;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.spi.common.UdtManagerUtil;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.bf.df.dfudt.udt.dfname.dfname.entity.IDFName;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class InstitutionalPlanDefaultValueDetermination extends AbstractDetermination {
   private IBqlExecuter bqlExecuter;

   public InstitutionalPlanDefaultValueDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData institutionalPlan = this.getData();
      WebSession webSession = CAFContext.current.getSession();
      String userId = webSession.getUserId();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("SYSUSER", userId)};
      String sqlselect = "SELECT BFADMINORGANIZATION1.ID DEPTID,BFADMINORGANIZATION1.NAME_CHS BEPTNAME,BFADMINORGANIZATION2.ID COMPID,BFADMINORGANIZATION2.NAME_CHS COMPNAME,BFEMPLOYEE.ID EMPID,BFEMPLOYEE.NAME_CHS EMPNAME FROM BFEMPLOYEE BFEMPLOYEE LEFT JOIN BFEMPLOYEESYSUSER BFEMPLOYEESYSUSER ON BFEMPLOYEE.ID = BFEMPLOYEESYSUSER.PARENTID LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION1 ON BFADMINORGANIZATION1.ID = BFEMPLOYEE.ORGANIZATION LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION2 ON BFADMINORGANIZATION2.ID = BFADMINORGANIZATION1.OWNERID WHERE BFEMPLOYEESYSUSER.SYSUSER = :SYSUSER";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> comp = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (CollectionUtils.isNotEmpty(comp)) {
         ICefValueObjManager valueObjMgr = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfname.DFName");
         if (!"".equals(valueOf(((DynamicResultRow)comp.get(0)).get("COMPID")))) {
            AssociationInfo dutyDept = new AssociationInfo();
            dutyDept.setValue("DutyDept", valueOf(((DynamicResultRow)comp.get(0)).get("DEPTID")));
            IDFName deptName = (IDFName)valueObjMgr.createDataType();
            deptName.setDFName(valueOf(((DynamicResultRow)comp.get(0)).get("BEPTNAME")));
            dutyDept.setValue("DutyDept_Name", deptName);
            institutionalPlan.setValue("DutyDept", dutyDept);
            AssociationInfo dutyComp = new AssociationInfo();
            dutyComp.setValue("DutyComp", valueOf(((DynamicResultRow)comp.get(0)).get("COMPID")));
            IDFName compName = (IDFName)valueObjMgr.createDataType();
            compName.setDFName(valueOf(((DynamicResultRow)comp.get(0)).get("COMPNAME")));
            dutyComp.setValue("DutyComp_Name", compName);
            institutionalPlan.setValue("DutyComp", dutyComp);
         } else {
            AssociationInfo dutyComp = new AssociationInfo();
            dutyComp.setValue("DutyComp", valueOf(((DynamicResultRow)comp.get(0)).get("DEPTID")));
            IDFName compName = (IDFName)valueObjMgr.createDataType();
            compName.setDFName(valueOf(((DynamicResultRow)comp.get(0)).get("BEPTNAME")));
            dutyComp.setValue("DutyComp_Name", compName);
            institutionalPlan.setValue("DutyComp", dutyComp);
         }

         AssociationInfo agent = new AssociationInfo();
         agent.setValue("Agent", valueOf(((DynamicResultRow)comp.get(0)).get("EMPID")));
         IDFName name = (IDFName)valueObjMgr.createDataType();
         name.setDFName(valueOf(((DynamicResultRow)comp.get(0)).get("EMPNAME")));
         agent.setValue("Agent_Name", name);
         institutionalPlan.setValue("Agent", agent);
      }

   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }

   public static String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
