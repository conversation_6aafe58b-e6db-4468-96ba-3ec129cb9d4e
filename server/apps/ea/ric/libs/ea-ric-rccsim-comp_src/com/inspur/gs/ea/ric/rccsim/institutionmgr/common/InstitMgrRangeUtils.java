package com.inspur.gs.ea.ric.rccsim.institutionmgr.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;

public final class InstitMgrRangeUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }

   public static String getCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Code");
   }

   public static void setCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Code", propertyValue);
   }

   public static AssoInfoBase getName(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Name");
   }

   public static void setName(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Name", propertyValue);
   }
}
