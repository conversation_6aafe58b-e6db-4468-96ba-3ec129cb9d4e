package com.inspur.gs.ea.ric.rccsim.institutiontemplate.common;

import com.inspur.edp.bef.api.BefRtBeanUtil;
import com.inspur.edp.bef.api.be.MgrActionUtils;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.component.attachment.AttachInfo;

public final class InstitutionTemplateMgrActionUtils {
   public static IEntityData BatchUploadAttachment(AttachInfo batchUploadInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionTemplate");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "BatchUploadAttachment", new Object[]{batchUploadInfo});
   }

   public static IEntityData UpdateAttachment(AttachInfo updateAttachInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.rccsim.InstitutionTemplate");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "UpdateAttachment", new Object[]{updateAttachInfo});
   }
}
