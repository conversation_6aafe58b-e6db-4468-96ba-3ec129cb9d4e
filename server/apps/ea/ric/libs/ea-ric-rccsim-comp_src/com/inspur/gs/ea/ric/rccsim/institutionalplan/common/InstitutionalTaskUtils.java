package com.inspur.gs.ea.ric.rccsim.institutionalplan.common;

import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;

public final class InstitutionalTaskUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }

   public static String getInititutionalName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "InititutionalName");
   }

   public static void setInititutionalName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "InititutionalName", propertyValue);
   }

   public static String getExplanSubject(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ExplanSubject");
   }

   public static void setExplanSubject(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ExplanSubject", propertyValue);
   }

   public static String getDraftingUnit(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "DraftingUnit");
   }

   public static void setDraftingUnit(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "DraftingUnit", propertyValue);
   }

   public static String getClassification(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Classification");
   }

   public static void setClassification(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Classification", propertyValue);
   }

   public static String getGrade(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Grade");
   }

   public static void setGrade(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Grade", propertyValue);
   }

   public static String getHandle(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Handle");
   }

   public static void setHandle(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Handle", propertyValue);
   }
}
