package com.inspur.gs.ea.ric.rccsim.institutiontemplate.common;

import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.common.commonudt.udt.attachmentinfo.attachmentinfo.entity.IAttachmentInfo;
import java.util.Date;

public final class InstitutionTemplateUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static IAttachmentInfo getFileInfo(IEntityData data) {
      return (IAttachmentInfo)EntityDataUtils.getValue(data, "FileInfo");
   }

   public static void setFileInfo(IEntityData data, IAttachmentInfo propertyValue) {
      EntityDataUtils.setValue(data, "FileInfo", propertyValue);
   }

   public static IAdministrativeInfo getCreateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "CreateInfo");
   }

   public static void setCreateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "CreateInfo", propertyValue);
   }
}
