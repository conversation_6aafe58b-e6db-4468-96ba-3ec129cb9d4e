package com.inspur.gs.ea.ric.rccsim.instplanreason.common;

import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.udt.attachmentinfo.attachmentinfo.entity.IAttachmentInfo;

public final class InstPlanReasonFileUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }

   public static IAttachmentInfo getFileInfo(IEntityData data) {
      return (IAttachmentInfo)EntityDataUtils.getValue(data, "FileInfo");
   }

   public static void setFileInfo(IEntityData data, IAttachmentInfo propertyValue) {
      EntityDataUtils.setValue(data, "FileInfo", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }
}
