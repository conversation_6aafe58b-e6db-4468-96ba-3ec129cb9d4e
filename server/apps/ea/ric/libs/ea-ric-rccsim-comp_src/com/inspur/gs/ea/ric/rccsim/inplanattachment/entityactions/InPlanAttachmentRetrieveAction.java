package com.inspur.gs.ea.ric.rccsim.inplanattachment.entityactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bef.api.be.IBENodeEntityContext;
import com.inspur.edp.bef.spi.action.AbstractAction;
import com.inspur.edp.cef.entity.entity.IEntityData;

public class InPlanAttachmentRetrieveAction extends AbstractAction<VoidActionResult> {
   public InPlanAttachmentRetrieveAction(IBENodeEntityContext beContext) {
      super(beContext);
   }

   public void execute() {
   }

   private IEntityData getData() {
      return this.getBEContext().getCurrentData();
   }
}
