package com.inspur.gs.ea.ric.rccsim.institutionmgr.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import java.util.Date;

public final class InstitutionMgrUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static String getCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Code");
   }

   public static void setCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Code", propertyValue);
   }

   public static String getName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Name");
   }

   public static void setName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Name", propertyValue);
   }

   public static String getOfficialDocNum(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "OfficialDocNum");
   }

   public static void setOfficialDocNum(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "OfficialDocNum", propertyValue);
   }

   public static AssoInfoBase getPublishDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "PublishDepart");
   }

   public static void setPublishDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "PublishDepart", propertyValue);
   }

   public static Date getPublishDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "PublishDate");
   }

   public static void setPublishDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "PublishDate", propertyValue);
   }

   public static AssoInfoBase getCategory(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Category");
   }

   public static void setCategory(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Category", propertyValue);
   }

   public static String getSlaveSystem(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "SlaveSystem");
   }

   public static void setSlaveSystem(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "SlaveSystem", propertyValue);
   }

   public static String getKeyWord(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "KeyWord");
   }

   public static void setKeyWord(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "KeyWord", propertyValue);
   }

   public static String getAbstract(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Abstract");
   }

   public static void setAbstract(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Abstract", propertyValue);
   }

   public static String getRegistDepart(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "RegistDepart");
   }

   public static void setRegistDepart(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "RegistDepart", propertyValue);
   }

   public static String getInsStatus(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "InsStatus");
   }

   public static void setInsStatus(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "InsStatus", propertyValue);
   }

   public static String getRegistrant(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Registrant");
   }

   public static void setRegistrant(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Registrant", propertyValue);
   }

   public static Date getRegistDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "RegistDate");
   }

   public static void setRegistDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "RegistDate", propertyValue);
   }

   public static String getRuleType(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "RuleType");
   }

   public static void setRuleType(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "RuleType", propertyValue);
   }

   public static AssoInfoBase getDocLevel(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "DocLevel");
   }

   public static void setDocLevel(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "DocLevel", propertyValue);
   }

   public static AssoInfoBase getMgrDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "MgrDepart");
   }

   public static void setMgrDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "MgrDepart", propertyValue);
   }

   public static String getRuleVersion(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "RuleVersion");
   }

   public static void setRuleVersion(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "RuleVersion", propertyValue);
   }

   public static AssoInfoBase getRange(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Range");
   }

   public static void setRange(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Range", propertyValue);
   }

   public static IBillState getBillState(IEntityData data) {
      return (IBillState)EntityDataUtils.getValue(data, "BillState");
   }

   public static void setBillState(IEntityData data, IBillState propertyValue) {
      EntityDataUtils.setValue(data, "BillState", propertyValue);
   }

   public static IProcessInstance getBillProcess(IEntityData data) {
      return (IProcessInstance)EntityDataUtils.getValue(data, "BillProcess");
   }

   public static void setBillProcess(IEntityData data, IProcessInstance propertyValue) {
      EntityDataUtils.setValue(data, "BillProcess", propertyValue);
   }

   public static IAdministrativeInfo getUpdateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "UpdateInfo");
   }

   public static void setUpdateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "UpdateInfo", propertyValue);
   }

   public static String getPublishDate01(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "PublishDate01");
   }

   public static void setPublishDate01(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "PublishDate01", propertyValue);
   }

   public static String getOldId(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "OldId");
   }

   public static void setOldId(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "OldId", propertyValue);
   }

   public static String getAbstract01(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Abstract01");
   }

   public static void setAbstract01(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Abstract01", propertyValue);
   }

   public static String getisLatest(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "isLatest");
   }

   public static void setisLatest(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "isLatest", propertyValue);
   }

   public static String getRuleTypeIndepCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "RuleTypeIndepCode");
   }

   public static void setRuleTypeIndepCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "RuleTypeIndepCode", propertyValue);
   }

   public static String getRuleTypeIndepName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "RuleTypeIndepName");
   }

   public static void setRuleTypeIndepName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "RuleTypeIndepName", propertyValue);
   }

   public static String getDispatchOffice(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "DispatchOffice");
   }

   public static void setDispatchOffice(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "DispatchOffice", propertyValue);
   }

   public static AssoInfoBase getAffiliateBusiness(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "AffiliateBusiness");
   }

   public static void setAffiliateBusiness(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "AffiliateBusiness", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static AssoInfoBase getInstitutionalPlan(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "InstitutionalPlan");
   }

   public static void setInstitutionalPlan(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "InstitutionalPlan", propertyValue);
   }

   public static String getBusinessCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "BusinessCode");
   }

   public static void setBusinessCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "BusinessCode", propertyValue);
   }

   public static AssoInfoBase getCountry(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Country");
   }

   public static void setCountry(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Country", propertyValue);
   }

   public static IEntityDataCollection getInstitMgrRanges(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "InstitMgrRange");
   }

   public static IEntityDataCollection getSubBusinesss(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "SubBusiness");
   }

   public static IEntityDataCollection getSystemMerges(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "SystemMerge");
   }

   public static IEntityDataCollection getInstitFiless(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "InstitFiles");
   }
}
