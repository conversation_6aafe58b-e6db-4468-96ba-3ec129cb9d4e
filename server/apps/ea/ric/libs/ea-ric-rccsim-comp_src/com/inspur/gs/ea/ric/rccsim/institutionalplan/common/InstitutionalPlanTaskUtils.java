package com.inspur.gs.ea.ric.rccsim.institutionalplan.common;

import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;

public final class InstitutionalPlanTaskUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }
}
