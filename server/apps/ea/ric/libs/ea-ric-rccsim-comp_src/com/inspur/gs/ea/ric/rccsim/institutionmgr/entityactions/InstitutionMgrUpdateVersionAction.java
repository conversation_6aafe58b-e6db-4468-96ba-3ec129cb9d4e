package com.inspur.gs.ea.ric.rccsim.institutionmgr.entityactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bef.api.be.IBENodeEntityContext;
import com.inspur.edp.bef.api.lcp.ILcpFactory;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bef.spi.action.RootAbstractAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cdp.coderule.api.CodeRuleService;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.common.commonudt.billstate.entity.BillStateEnum;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.dataaccess.DbType;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.commons.utils.StringUtils;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class InstitutionMgrUpdateVersionAction extends RootAbstractAction<VoidActionResult> {
   private IBqlExecuter bqlExecuter;

   public InstitutionMgrUpdateVersionAction(IBENodeEntityContext beContext) {
      super(beContext);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      ArrayList<String> instIDs = new ArrayList();
      String instId = data.getID();
      String instCode = (String)data.getValue("Code");
      String instBusinessCode = (String)data.getValue("BusinessCode");
      if (null == instBusinessCode) {
         Map<String, Object> map = new HashMap();
         instBusinessCode = ((CodeRuleService)SpringBeanUtils.getBean(CodeRuleService.class)).generate("b6e75786-1651-41a8-8df8-5f585923ef0b", map);
         data.setValue("BusinessCode", instBusinessCode);
         IDbParameter[] updateParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", data.getID()), this.getBqlExecuter().makeInOutParam("instBusinessCode", instBusinessCode)};
         String updateSql = "update EAINSTITUTIONMGR set BusinessCode = :instBusinessCode where id = :ID";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeBqlStatement(updateSql, instIDs, updateParams);
      }

      String instName = (String)data.getValue("Name");
      WebSession webSession = CAFContext.current.getSession();
      String userName = webSession.getUserName();
      String creator = ((IAdministrativeInfo)data.getValue("UpdateInfo")).getCreatedBy();
      Date createTime = ((IAdministrativeInfo)data.getValue("UpdateInfo")).getCreatedOn();
      Date updateTime = ((IAdministrativeInfo)data.getValue("UpdateInfo")).getLastChangedOn();
      String updatePer = ((IAdministrativeInfo)data.getValue("UpdateInfo")).getLastChangedBy();
      Integer ruleType = 0;
      if ("Out".equals(data.getValue("RuleType"))) {
         ruleType = 1;
      } else if ("In".equals(data.getValue("RuleType"))) {
         ruleType = 0;
      }

      Integer billState = 2;
      Integer isLatest = 1;
      Integer insStatus = 0;
      if ("No".equals(data.getValue("RuleType"))) {
         insStatus = 0;
      } else if ("Yes".equals(data.getValue("RuleType"))) {
         insStatus = 1;
      }

      String range = (String)((AssociationInfo)data.getValue("Range")).getValue("Range");
      Integer slaveSystem = 0;
      if ("Dq".equals(data.getValue("RuleType"))) {
         slaveSystem = 0;
      } else if ("Ad".equals(data.getValue("RuleType"))) {
         slaveSystem = 1;
      }

      Date date = new Date();
      int ve = Integer.parseInt(((String)data.getValue("RuleVersion")).substring(1, 2));
      String ruleVersion = "V" + (ve + 1) + ".0";
      String id = UUID.randomUUID().toString();
      String officialDocNum = "";
      if (data.getValue("OfficialDocNum") != null && !"".equals(data.getValue("OfficialDocNum"))) {
         officialDocNum = (String)data.getValue("OfficialDocNum");
      }

      String docLevel = "";
      if (data.getValue("DocLevel") != null && ((AssociationInfo)data.getValue("DocLevel")).getValue("DocLevel_ID") != null) {
         docLevel = (String)((AssociationInfo)data.getValue("DocLevel")).getValue("DocLevel_ID");
      }

      SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd");
      String pd = (String)data.getValue("PublishDate01");
      if (pd == "" || pd == null) {
         pd = sf.format(new Date());
      }

      Date publishDate = (Date)data.getValue("PublishDate");
      if (publishDate == null) {
         publishDate = new Date();
      }

      String dept = (String)((AssociationInfo)data.getValue("PublishDepart")).getValue("PublishDepart_ID");
      if (StringUtils.isEmpty(dept)) {
         dept = "0";
      }

      String abstract1 = (String)data.getValue("Abstract01");
      if (StringUtils.isEmpty(abstract1)) {
         abstract1 = "0";
      }

      String institutionalPlanId = (String)((AssociationInfo)data.getValue("InstitutionalPlan")).getValue("InstitutionalPlan");
      String countryId = (String)((AssociationInfo)data.getValue("Country")).getValue("Country");
      String categoryId = (String)((AssociationInfo)data.getValue("Category")).getValue("Category");
      String affiliateBusiness = (String)((AssociationInfo)data.getValue("AffiliateBusiness")).getValue("AffiliateBusiness");
      IDbParameter[] params = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", id), this.getBqlExecuter().makeInOutParam("instCode", instCode), this.getBqlExecuter().makeInOutParam("instBusinessCode", instBusinessCode), this.getBqlExecuter().makeInOutParam("instName", instName), this.getBqlExecuter().makeInOutParam("officialDocNum", officialDocNum), this.getBqlExecuter().makeInOutParam("publishDepart", dept), this.getBqlExecuter().makeInOutParam("publishDate01", pd), this.getBqlExecuter().makeInOutParam("PublishDate", publishDate), this.getBqlExecuter().makeInOutParam("slaveSystem", slaveSystem), this.getBqlExecuter().makeInOutParam("keyWord", valueOf(data.getValue("KeyWord"))), this.getBqlExecuter().makeInOutParam("Abstract01", valueOf(data.getValue("Abstract01"))), this.getBqlExecuter().makeInOutParam("DispatchOffice", valueOf(data.getValue("DispatchOffice"))), this.getBqlExecuter().makeInOutParam("ruleType", ruleType), this.getBqlExecuter().makeInOutParam("docLevel", docLevel), this.getBqlExecuter().makeInOutParam("mgrDepart", valueOf(((AssociationInfo)data.getValue("MgrDepart")).getValue("MgrDepart"))), this.getBqlExecuter().makeInOutParam("ruleVersion", valueOf(data.getValue("RuleVersion"))), this.getBqlExecuter().makeInOutParam("range", range == null ? "" : range), this.getBqlExecuter().makeInOutParam("insStatus", insStatus), this.getBqlExecuter().makeInOutParam("isLatest", isLatest), this.getBqlExecuter().makeInOutParam("billState", billState), this.getBqlExecuter().makeInOutParam("userName", userName), this.getBqlExecuter().makeInOutParam("date", date), this.getBqlExecuter().makeInOutParam("creator", creator), this.getBqlExecuter().makeInOutParam("createTime", createTime), this.getBqlExecuter().makeInOutParam("updatePer", updatePer), this.getBqlExecuter().makeInOutParam("updateTime", updateTime), this.getBqlExecuter().makeInOutParam("version", data.getValue("Version")), this.getBqlExecuter().makeInOutParam("isexists", "0"), this.getBqlExecuter().makeInOutParam("institutionalPlan", institutionalPlanId == null ? "" : institutionalPlanId), this.getBqlExecuter().makeInOutParam("country", countryId == null ? "" : countryId), this.getBqlExecuter().makeInOutParam("Category", categoryId == null ? "" : categoryId), this.getBqlExecuter().makeInOutParam("AffiliateBusiness", affiliateBusiness == null ? "" : affiliateBusiness), this.getBqlExecuter().makeInOutParam("billProcess", valueOf(((IProcessInstance)data.getValue("BillProcess")).getProcessInstance()))};
      DbType dbType = CAFContext.current.getDbType();
      String sqlInsertQues = "";
      if (DbType.MySQL.equals(dbType)) {
         sqlInsertQues = "insert into GSXMRCINSTITUTIONMGRHISTORY(ID,CODE,BUSINESSCODE,NAME,OFFICIALDOCNUM,PUBLISHDEPART,PUBLISHDATE01,SLAVESYSTEM,KEYWORD,ABSTRACT01,UPDATEINFO_CREATEDBY,UPDATEINFO_CREATEDON,ISEXISTS,ISLATEST,RULETYPE,DOCLEVEL,MGRDEPART,RULEVERSION,`RANGE`,BILLSTATE,UPDATEINFO_LASTCHANGEDBY,UPDATEINFO_LASTCHANGEDON,INSERTPERSON,INSERTDATE,INSSTATUS,VERSION,BILLPROCESS,institutionalPlan,COUNTRY,Category,AffiliateBusiness,PublishDate,DispatchOffice)values(:ID,:instCode,:instBusinessCode,:instName,:officialDocNum,:publishDepart,:publishDate01,:slaveSystem,:keyWord,:Abstract01,:creator,:createTime,:isexists,:isLatest,:ruleType,:docLevel,:mgrDepart,:ruleVersion,:range,:billState,:updatePer,:updateTime,:userName,:date,:insStatus,:version,:billProcess,:institutionalPlan,:country,:Category,:AffiliateBusiness,:PublishDate,:DispatchOffice)";
      } else {
         sqlInsertQues = "insert into GSXMRCINSTITUTIONMGRHISTORY(ID,CODE,BUSINESSCODE,NAME,OFFICIALDOCNUM,PUBLISHDEPART,PUBLISHDATE01,SLAVESYSTEM,KEYWORD,ABSTRACT01,UPDATEINFO_CREATEDBY,UPDATEINFO_CREATEDON,ISEXISTS,ISLATEST,RULETYPE,DOCLEVEL,MGRDEPART,RULEVERSION,RANGE,BILLSTATE,UPDATEINFO_LASTCHANGEDBY,UPDATEINFO_LASTCHANGEDON,INSERTPERSON,INSERTDATE,INSSTATUS,VERSION,BILLPROCESS,institutionalPlan,COUNTRY,Category,AffiliateBusiness,PublishDate,DispatchOffice)values(:ID,:instCode,:instBusinessCode,:instName,:officialDocNum,:publishDepart,:publishDate01,:slaveSystem,:keyWord,:Abstract01,:creator,:createTime,:isexists,:isLatest,:ruleType,:docLevel,:mgrDepart,:ruleVersion,:range,:billState,:updatePer,:updateTime,:userName,:date,:insStatus,:version,:billProcess,:institutionalPlan,:country,:Category,:AffiliateBusiness,:PublishDate,:DispatchOffice)";
      }

      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      this.bqlExecuter.executeBqlStatement(sqlInsertQues, instIDs, params);
      List<DynamicResultRow> businessList = this.getBusiness(instId);
      ArrayList<String> busiIDs = new ArrayList();

      for(int i = 0; i < businessList.size(); ++i) {
         String bid = UUID.randomUUID().toString();
         IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Id", bid), this.getBqlExecuter().makeInOutParam("ParentID", id), this.getBqlExecuter().makeInOutParam("deptName", ((DynamicResultRow)businessList.get(i)).get("name").toString())};
         String sqlInsertQues1 = "insert into GSXMRCSUBBUSINESSHISTORY (Id,ParentID,Name)values(:Id,:ParentID,:deptName)";
         this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
      }

      List<DynamicResultRow> rangeList = this.getRange(instId);

      for(int i = 0; i < rangeList.size(); ++i) {
         String bid = UUID.randomUUID().toString();
         IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Id", bid), this.getBqlExecuter().makeInOutParam("ParentID", id), this.getBqlExecuter().makeInOutParam("deptName", ((DynamicResultRow)rangeList.get(i)).get("name").toString())};
         String sqlInsertQues1 = "insert into EAINSTITMGRRANGEHIS (Id,ParentID,Name)values(:Id,:ParentID,:deptName)";
         this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
      }

      List<DynamicResultRow> fileList = this.getFile(instId);

      for(int i = 0; i < fileList.size(); ++i) {
         String bid = UUID.randomUUID().toString();
         IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", bid), this.getBqlExecuter().makeInOutParam("PARENTID", id), this.getBqlExecuter().makeInOutParam("FILEINFO", ((DynamicResultRow)fileList.get(i)).get("FILEINFO").toString())};
         String sqlInsertQues1 = "insert into GSXMRCINSTITUTIONMGRFILEHIS (ID,PARENTID,FILEINFO)values(:ID,:PARENTID,:FILEINFO)";
         this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
      }

      List<DynamicResultRow> fileysList = this.getFileInfos(instId);

      for(int i = 0; i < fileysList.size(); ++i) {
         String bid = UUID.randomUUID().toString();
         IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", bid), this.getBqlExecuter().makeInOutParam("BillID", id), this.getBqlExecuter().makeInOutParam("AttachFileManage", ((DynamicResultRow)fileysList.get(i)).get("AttachFileManage").toString()), this.getBqlExecuter().makeInOutParam("updateInfo_createdby", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_createdby").toString()), this.getBqlExecuter().makeInOutParam("updateInfo_createdon", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_createdon")), this.getBqlExecuter().makeInOutParam("updateInfo_lastchangedby", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_lastchangedby").toString()), this.getBqlExecuter().makeInOutParam("updateInfo_lastchangedon", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_lastchangedon"))};
         String sqlInsertQues1 = "insert into eaAttachMentHis (ID,BillID,AttachFileManage,updateInfo_createdby,updateInfo_createdon,updateInfo_lastchangedby,updateInfo_lastchangedon)values(:ID,:BillID,:AttachFileManage,:updateInfo_createdby,:updateInfo_createdon,:updateInfo_lastchangedby,:updateInfo_lastchangedon)";
         this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
      }

      ModifyChangeDetail change = new ModifyChangeDetail(data.getID());
      ValueObjModifyChangeDetail billStateChange = new ValueObjModifyChangeDetail();
      billStateChange.getPropertyChanges().put("BillState", BillStateEnum.Billing);
      change.getPropertyChanges().put("BillState", billStateChange);
      ValueObjModifyChangeDetail updateInfoChange = new ValueObjModifyChangeDetail();
      updateInfoChange.getPropertyChanges().put("CreatedBy", userName);
      updateInfoChange.getPropertyChanges().put("CreatedOn", new Date());
      updateInfoChange.getPropertyChanges().put("LastChangedBy", userName);
      updateInfoChange.getPropertyChanges().put("LastChangedOn", new Date());
      change.getPropertyChanges().put("UpdateInfo", updateInfoChange);
      change.getPropertyChanges().put("RuleVersion", ruleVersion);
      AssociationInfo institutionalPlan = new AssociationInfo();
      institutionalPlan.setValue("InstitutionalPlan", "");
      institutionalPlan.setValue("InstitutionalPlan_DocName", "");
      change.getPropertyChanges().put("InstitutionalPlan", institutionalPlan);
      IStandardLcp lcp = ((ILcpFactory)SpringBeanUtils.getBean(ILcpFactory.class)).createLcp(this.getBEContext().getBizEntity().getBEType());
      lcp.modify(change);
   }

   public List<DynamicResultRow> getBusiness(String instId) {
      IEntityData data = this.getData();
      instId = data.getID();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select Id,parentId,Code,Name from EASubBusiness where ParentID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> businessList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return businessList;
   }

   public List<DynamicResultRow> getRange(String instId) {
      IEntityData data = this.getData();
      instId = data.getID();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select Id,parentId,Code,Name from EAInstitMgrRange where ParentID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> businessList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return businessList;
   }

   public List<DynamicResultRow> getFile(String instId) {
      IEntityData data = this.getData();
      instId = data.getID();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select ID,PARENTID,FILEINFO from EAINSTITFILES where PARENTID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> fileList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return fileList;
   }

   public List<DynamicResultRow> getFileInfos(String instId) {
      IEntityData data = this.getData();
      instId = data.getID();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select ID,BillID,AttachFileManage,updateInfo_createdby,updateInfo_createdon,updateInfo_lastchangedby,updateInfo_lastchangedon from eaAttachMent where BillID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> fileList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return fileList;
   }

   public static String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }

   private IEntityData getData() {
      return this.getBEContext().getCurrentData();
   }
}
