package com.inspur.gs.ea.ric.rccsim.webservicecmd;

import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

@GspServiceBundle(
   applicationName = "ea",
   serviceUnitName = "ric",
   serviceName = "ric_institutionplanservice"
)
public interface InstitutionPlanDown {
   @RpcServiceMethod(
      serviceId = "com.inspur.gs.ea.ric.rccsim.webservicecmd.InstitutionPlanDown.institutionPlanDownChange"
   )
   void institutionPlanDownChange(@RpcParam(paramName = "flag") String var1, @RpcParam(paramName = "ID") String var2, @RpcParam(paramName = "PlanID") String var3, @RpcParam(paramName = "Reason") String var4);
}
