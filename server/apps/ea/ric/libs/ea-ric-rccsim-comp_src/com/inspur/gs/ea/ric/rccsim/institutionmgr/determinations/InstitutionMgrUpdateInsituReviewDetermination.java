package com.inspur.gs.ea.ric.rccsim.institutionmgr.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractB4SaveDetermination;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cdp.coderule.api.CodeRuleService;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InstitutionMgrUpdateInsituReviewDetermination extends AbstractB4SaveDetermination {
   private IBqlExecuter bqlExecuter;

   public InstitutionMgrUpdateInsituReviewDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      ArrayList<String> busiIDs = new ArrayList();
      String id = data.getID();
      String name = (String)data.getValue("Name");
      String mgrDepart = (String)((AssociationInfo)((AssociationInfo)data.getValue("MgrDepart"))).getValue("MgrDepart");
      IBqlExecuter iBqlExecuter = this.getBqlExecuter();
      IDbParameter iDbParameters = this.bqlExecuter.makeInParam("Id", id);
      List<String> list = new ArrayList();
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String bqlStatement1 = "select ID from EAInstitutReview where id=:Id";
      List<DynamicResultRow> result = iBqlExecuter.executeSelectStatement(bqlStatement1, list, new IDbParameter[]{iDbParameters});
      if (CollectionUtils.isEmpty(result)) {
         Map<String, Object> map = new HashMap();
         String code = ((CodeRuleService)SpringBeanUtils.getBean(CodeRuleService.class)).generate("2499f6ca-89f6-4814-9a68-d919e18065d9", map);
         IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Id", id), this.getBqlExecuter().makeInOutParam("Code", code), this.getBqlExecuter().makeInOutParam("Name", name), this.getBqlExecuter().makeInOutParam("Organization", mgrDepart)};
         String sqlInsertQues1 = "insert into EAInstitutReview (Id,Name,Code,Organization) values(:Id,:Name,:Code,:Organization)";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
      }

   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
