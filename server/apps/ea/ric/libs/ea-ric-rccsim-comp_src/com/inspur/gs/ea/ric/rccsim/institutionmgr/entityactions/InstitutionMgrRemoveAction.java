package com.inspur.gs.ea.ric.rccsim.institutionmgr.entityactions;

import com.inspur.edp.bef.api.be.IBENodeEntityContext;
import com.inspur.edp.bef.spi.action.RootAbstractAction;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;

public class InstitutionMgrRemoveAction extends RootAbstractAction<Boolean> {
   private IBqlExecuter bqlExecuter;

   public InstitutionMgrRemoveAction(IBENodeEntityContext beContext) {
      super(beContext);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      String dataID = (String)data.getValue("ID");
      ArrayList<String> inst = new ArrayList();
      if (dataID != null && !dataID.isEmpty()) {
         IDbParameter[] params = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("isLatest", "2"), this.getBqlExecuter().makeInOutParam("id", dataID)};
         String updateSql = "UPDATE EAINSTITUTIONMGR SET ISLATEST=:isLatest WHERE ID=:id";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeBqlStatement(updateSql, inst, params);
      }

   }

   private IEntityData getData() {
      return this.getBEContext().getCurrentData();
   }
}
