package com.inspur.gs.ea.ric.rccsim.bizactions;

import com.inspur.edp.bef.api.be.IBEManagerContext;
import com.inspur.edp.bef.api.be.IBEService;
import com.inspur.edp.bef.api.be.IBusinessEntity;
import com.inspur.edp.bef.spi.action.AbstractManagerAction;

public class InstitutionMgrRemoveMgrAction extends AbstractManagerAction<Boolean> {
   private String dataID;

   public InstitutionMgrRemoveMgrAction(IBEManagerContext managerContext, String dataID) {
      super(managerContext);
      this.dataID = dataID;
   }

   public void execute() {
      IBusinessEntity entity = this.getEntity(this.dataID);
      entity.retrieve();
      entity.executeBizAction("Remove", new Object[0]);
      this.setResult(true);
   }

   private IBusinessEntity getEntity(String dataId) {
      return super.getBEManagerContext().getEntity(dataId);
   }

   private IBEService getMgr() {
      return super.getBEManagerContext().getBEManager();
   }
}
