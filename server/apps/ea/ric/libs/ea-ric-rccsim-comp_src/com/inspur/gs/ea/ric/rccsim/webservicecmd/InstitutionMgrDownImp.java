package com.inspur.gs.ea.ric.rccsim.webservicecmd;

import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.Date;
import org.springframework.util.StringUtils;

public class InstitutionMgrDownImp implements InstitutionMgrDown {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void institutionMgrDownChange(String flag, String ID, String categoryId, String docId) {
      if ("Pass".equals(flag) && !StringUtils.isEmpty(ID)) {
         IBqlExecuter bqlExecuter = this.getBqlExecuter();
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("7cdc0646-2a4d-474e-be60-338f4af635ca");
         IDbParameter[] iDbParameters = new IDbParameter[1];
         iDbParameters[0] = bqlExecuter.makeInParam("CL", ID);
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         String bql = "update EAInstitutionMgr  set isLatest = '0' where ID = :CL";
         bqlExecuter.executeBqlStatement(bql, refEntityIDs, iDbParameters);
      }

   }

   public void updateInstitutionPlan(String flag, String ID, String planId) {
      if ("Pass".equals(flag) && !StringUtils.isEmpty(planId)) {
         ArrayList<String> planIDs = new ArrayList();
         IDbParameter[] updateParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", planId), this.getBqlExecuter().makeInOutParam("FinishTime", new Date()), this.getBqlExecuter().makeInOutParam("IsExecution", '1')};
         String updateSql = "update EAInstitutionalPlan set FinishTime = :FinishTime,IsExecution = :IsExecution where ID = :ID";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeBqlStatement(updateSql, planIDs, updateParams);
      }

   }
}
