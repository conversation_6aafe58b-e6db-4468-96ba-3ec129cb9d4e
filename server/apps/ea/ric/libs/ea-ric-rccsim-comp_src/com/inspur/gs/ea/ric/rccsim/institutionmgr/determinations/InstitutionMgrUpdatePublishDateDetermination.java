package com.inspur.gs.ea.ric.rccsim.institutionmgr.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

public class InstitutionMgrUpdatePublishDateDetermination extends AbstractDetermination {
   private IBqlExecuter bqlExecuter;

   public InstitutionMgrUpdatePublishDateDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      new ArrayList();
      new ArrayList();
      String publishDate = (String)data.getValue("PublishDate01");
      Integer ruleType = 0;
      if ("Out".equals(data.getValue("RuleType"))) {
         ruleType = 1;
      } else if ("In".equals(data.getValue("RuleType"))) {
         ruleType = 0;
      }

      IBillState billState = (IBillState)data.getValue("BillState");
      String categoryId = (String)((AssociationInfo)((AssociationInfo)data.getValue("Category"))).getValue("Category_ID");
      String docId = (String)((AssociationInfo)((AssociationInfo)data.getValue("DocLevel"))).getValue("DocLevel_ID");
      IDbParameter[] var10000 = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Code", !"".equals(data.getValue("Code")) && data.getValue("Code") != null ? data.getValue("Code") : ""), this.getBqlExecuter().makeInOutParam("categoryId", !"".equals(categoryId) && categoryId != null ? categoryId : ""), this.getBqlExecuter().makeInOutParam("docId", !"".equals(docId) && docId != null ? docId : "")};
      if (billState.getBillState().getValue() == 2 && ruleType == 0) {
         SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd");
         df.format(new Date());
         data.setValue("PublishDate01", publishDate);
      }

      if (billState.getBillState().getValue() == 3 && ruleType == 0) {
      }

   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
