package com.inspur.gs.ea.ric.rccsim.institutionmgr.entityactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bef.api.be.IBENodeEntityContext;
import com.inspur.edp.bef.spi.action.RootAbstractAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.dataaccess.DbType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class InstitutionMgrSaveRuleVersionAction extends RootAbstractAction<VoidActionResult> {
   private IBqlExecuter bqlExecuter;

   public InstitutionMgrSaveRuleVersionAction(IBENodeEntityContext beContext) {
      super(beContext);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      String instCode = (String)data.getValue("Code");
      String instBusinessCode = (String)data.getValue("BusinessCode");
      ArrayList<String> inst = new ArrayList();
      ArrayList<String> inst01 = new ArrayList();
      List<DynamicResultRow> institutionList = this.getInstitution(instCode, instBusinessCode);
      if (institutionList.size() > 0) {
         String id = ((DynamicResultRow)institutionList.get(0)).get("id").toString();
         IDbParameter[] params = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Code", !"".equals(((DynamicResultRow)institutionList.get(0)).get("code")) && ((DynamicResultRow)institutionList.get(0)).get("code") != null ? ((DynamicResultRow)institutionList.get(0)).get("code").toString() : ""), this.getBqlExecuter().makeInOutParam("BusinessCode", !"".equals(((DynamicResultRow)institutionList.get(0)).get("businessCode")) && ((DynamicResultRow)institutionList.get(0)).get("businessCode") != null ? ((DynamicResultRow)institutionList.get(0)).get("businessCode").toString() : "")};
         DbType dbType = CAFContext.current.getDbType();
         String rangestr = "";
         if (DbType.MySQL.equals(dbType)) {
            rangestr = "`RANGE`, ";
         } else {
            rangestr = "RANGE, ";
         }

         String sqlInsertQues2 = "insert into EAINSTITUTIONMGR(ID,                                       ABSTRACT,                                       BILLPROCESS,                                       BILLSTATE,                                       CATEGORY,                                        CODE,                                        DOCLEVEL,                                        INSSTATUS,                                        KEYWORD,                                        MGRDEPART,                                        NAME,                                        OFFICIALDOCNUM,                                        PUBLISHDATE,                                        PUBLISHDEPART, " + rangestr + "                                       REGISTDATE,                                        REGISTDEPART,                                        REGISTRANT,                                        RULETYPE,                                        RULEVERSION,                                        SLAVESYSTEM,                                        UPDATEINFO_CREATEDBY,                                        UPDATEINFO_CREATEDON,                                        UPDATEINFO_LASTCHANGEDBY,                                        UPDATEINFO_LASTCHANGEDON,                                        VERSION,                                        PUBLISHDATE01,                                        ABSTRACT01,                                       ISLATEST,institutionalPlan,BUSINESSCODE,COUNTRY,AffiliateBusiness,DispatchOffice)  select t.id,t.abstract,t.billprocess,t.billstate, t.category,t.code,t.doclevel, 1 ,        t.keyword,        t.mgrdepart,        t.name,        t.officialdocnum,        t.publishdate,        t.publishdepart,        t.range,        t.registdate,        t.registdepart,        t.registrant,        t.ruletype,        t.ruleversion,        t.slavesystem,        t.updateinfo_createdby,        t.updateinfo_createdon,        t.updateinfo_lastchangedby,        t.updateinfo_lastchangedon,        t.version,        t.publishdate01,        t.abstract01,       1,t.institutionalPlan,t.BUSINESSCODE,t.COUNTRY,t.AffiliateBusiness,t.DispatchOffice from GSXMRCINSTITUTIONMGRHISTORY t inner join (select name,         max(insertdate) maxdate  from GSXMRCINSTITUTIONMGRHISTORY   group by name) b on t.name=b.name and t.insertdate=b.maxdate where t.isexists='0' and (t.code=:Code or t.businessCode=:BusinessCode)";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeBqlStatement(sqlInsertQues2, inst, params);
         String sqlUpdateQues = "update GSXMRCINSTITUTIONMGRHISTORY set isexists=1 where code=:Code or businessCode=:BusinessCode";
         this.bqlExecuter.executeBqlStatement(sqlUpdateQues, inst01, params);
         data.setValue("OldId", id);
         new ArrayList();
         List<DynamicResultRow> businessList = this.getBusiness(id);
         ArrayList<String> busiIDs = new ArrayList();

         for(int i = 0; i < businessList.size(); ++i) {
            IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Id", ((DynamicResultRow)businessList.get(i)).get("Id").toString()), this.getBqlExecuter().makeInOutParam("ParentID", id), this.getBqlExecuter().makeInOutParam("deptName", !"".equals(((DynamicResultRow)businessList.get(i)).get("name")) && ((DynamicResultRow)businessList.get(i)).get("name") != null ? ((DynamicResultRow)businessList.get(i)).get("name").toString() : "")};
            String sqlInsertQues1 = "insert into EASUBBUSINESS (Id,ParentID,Name)values(:Id,:ParentID,:deptName)";
            this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
         }

         List<DynamicResultRow> rangeList = this.getRange(id);

         for(int i = 0; i < rangeList.size(); ++i) {
            IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Id", ((DynamicResultRow)rangeList.get(i)).get("Id").toString()), this.getBqlExecuter().makeInOutParam("ParentID", id), this.getBqlExecuter().makeInOutParam("deptName", !"".equals(((DynamicResultRow)rangeList.get(i)).get("name")) && ((DynamicResultRow)rangeList.get(i)).get("name") != null ? ((DynamicResultRow)rangeList.get(i)).get("name").toString() : "")};
            String sqlInsertQues1 = "insert into EAINSTITMGRRANGE (Id,ParentID,Name)values(:Id,:ParentID,:deptName)";
            this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
         }

         List<DynamicResultRow> fileList = this.getFile(id);

         for(int i = 0; i < fileList.size(); ++i) {
            IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", ((DynamicResultRow)fileList.get(i)).get("ID").toString()), this.getBqlExecuter().makeInOutParam("PARENTID", id), this.getBqlExecuter().makeInOutParam("FILEINFO", ((DynamicResultRow)fileList.get(i)).get("FILEINFO").toString())};
            String sqlInsertQues1 = "insert into EAINSTITFILES (ID,PARENTID,FILEINFO)values(:ID,:PARENTID,:FILEINFO)";
            this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
         }

         List<DynamicResultRow> fileysList = this.getFileInfos(id);

         for(int i = 0; i < fileysList.size(); ++i) {
            String bid = UUID.randomUUID().toString();
            IDbParameter[] deptParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", ((DynamicResultRow)fileysList.get(i)).get("ID").toString()), this.getBqlExecuter().makeInOutParam("BillID", id), this.getBqlExecuter().makeInOutParam("AttachFileManage", ((DynamicResultRow)fileysList.get(i)).get("AttachFileManage").toString()), this.getBqlExecuter().makeInOutParam("updateInfo_createdby", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_createdby").toString()), this.getBqlExecuter().makeInOutParam("updateInfo_createdon", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_createdon")), this.getBqlExecuter().makeInOutParam("updateInfo_lastchangedby", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_lastchangedby").toString()), this.getBqlExecuter().makeInOutParam("updateInfo_lastchangedon", ((DynamicResultRow)fileysList.get(i)).get("updateInfo_lastchangedon"))};
            String sqlInsertQues1 = "insert into eaAttachMent (ID,BillID,AttachFileManage,updateInfo_createdby,updateInfo_createdon,updateInfo_lastchangedby,updateInfo_lastchangedon,BillDetailID)values(:ID,:BillID,:AttachFileManage,:updateInfo_createdby,:updateInfo_createdon,:updateInfo_lastchangedby,:updateInfo_lastchangedon,'')";
            this.bqlExecuter.executeBqlStatement(sqlInsertQues1, busiIDs, deptParams);
         }
      }

   }

   public List<DynamicResultRow> getInstitution(String code, String businessCode) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("Code", code), this.getBqlExecuter().makeInOutParam("BusinessCode", businessCode)};
      String sqlSelectOrg = "select t.id,t.abstract,t.billprocess,t.billstate, t.category,t.code,t.businessCode,t.COUNTRY,t.doclevel, t.insstatus,t.keyword, t.mgrdepart, t.name,t.officialdocnum,t.publishdate, t.publishdepart,t.range,t.registdate,t.registdepart,t.registrant,t.ruletype,t.ruleversion,t.slavesystem,t.updateinfo_createdby,t.updateinfo_createdon, t.updateinfo_lastchangedby,t.updateinfo_lastchangedon,t.version,t.publishdate01,t.oldid,t.abstract01, t.insertdate from GSXMRCINSTITUTIONMGRHISTORY t inner join (select name,         max(insertdate) maxdate  from GSXMRCINSTITUTIONMGRHISTORY   group by name) b on t.name=b.name and t.insertdate=b.maxdate where t.isexists='0' and (t.code=:Code or t.businessCode = :BusinessCode)";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> businessList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return businessList;
   }

   public List<DynamicResultRow> getBusiness(String instId) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select Id,parentId,Code,Name from GSXMRCSUBBUSINESSHISTORY where ParentID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> businessList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return businessList;
   }

   public List<DynamicResultRow> getRange(String instId) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select Id,parentId,Code,Name from EAINSTITMGRRANGEHIS where ParentID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> businessList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return businessList;
   }

   public List<DynamicResultRow> getFile(String instId) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectparams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select ID,PARENTID,FILEINFO from GSXMRCINSTITUTIONMGRFILEHIS where PARENTID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> fileList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectparams);
      return fileList;
   }

   public List<DynamicResultRow> getFileInfos(String instId) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", instId)};
      String sqlSelectOrg = "select ID,BillID,AttachFileManage,updateInfo_createdby,updateInfo_createdon,updateInfo_lastchangedby,updateInfo_lastchangedon from eaAttachMentHis where BillID=:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> fileList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return fileList;
   }

   private IEntityData getData() {
      return this.getBEContext().getCurrentData();
   }
}
