package com.inspur.gs.ea.ric.rccsim.institutionmgr.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.api.manager.ICefValueObjManager;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.spi.common.UdtManagerUtil;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.bf.df.dfudt.udt.dfname.dfname.entity.IDFName;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class InstitutionMgrRegistDepartDetermination extends AbstractDetermination {
   private IBqlExecuter bqlExecuter;

   public InstitutionMgrRegistDepartDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      WebSession webSession = CAFContext.current.getSession();
      String userCode = webSession.getUserCode();
      String userId = webSession.getUserId();
      String userName = webSession.getUserName();
      String deptId = webSession.getSysOrgId();
      IBqlExecuter iBqlExecuter = this.getBqlExecuter();
      IDbParameter iDbParameters = this.bqlExecuter.makeInParam("SYSUSER", userId);
      List<String> list = new ArrayList();
      list.add("6f56da3c-4b62-40a4-95de-20c18695dcca");
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String bqlStatement1 = "SELECT BFADMINORGANIZATION1.ID DEPTID,BFADMINORGANIZATION1.NAME_CHS DEPTNAME,BFADMINORGANIZATION2.ID COMPID,BFADMINORGANIZATION2.NAME_CHS COMPNAME,BFEMPLOYEE.ID EMPID,BFEMPLOYEE.NAME_CHS EMPNAME FROM BFEMPLOYEE BFEMPLOYEE LEFT JOIN BFEMPLOYEESYSUSER BFEMPLOYEESYSUSER ON BFEMPLOYEE.ID = BFEMPLOYEESYSUSER.PARENTID LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION1 ON BFADMINORGANIZATION1.ID = BFEMPLOYEE.ORGANIZATION LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION2 ON BFADMINORGANIZATION2.ID = BFADMINORGANIZATION1.OWNERID WHERE BFEMPLOYEESYSUSER.SYSUSER = :SYSUSER";
      List<DynamicResultRow> result = iBqlExecuter.executeSelectStatement(bqlStatement1, list, new IDbParameter[]{iDbParameters});
      if (CollectionUtils.isNotEmpty(result)) {
         ICefValueObjManager valueObjMgr = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfname.DFName");
         if (!"".equals(valueOf(((DynamicResultRow)result.get(0)).get("COMPID")))) {
            AssociationInfo mgrDept = new AssociationInfo();
            mgrDept.setValue("MgrDepart", valueOf(((DynamicResultRow)result.get(0)).get("DEPTID")));
            IDFName deptName = (IDFName)valueObjMgr.createDataType();
            deptName.setDFName(valueOf(((DynamicResultRow)result.get(0)).get("DEPTNAME")));
            mgrDept.setValue("MgrDepart_Name", deptName);
            data.setValue("MgrDepart", mgrDept);
            AssociationInfo publishDepart = new AssociationInfo();
            publishDepart.setValue("PublishDepart", valueOf(((DynamicResultRow)result.get(0)).get("COMPID")));
            IDFName compName = (IDFName)valueObjMgr.createDataType();
            compName.setDFName(valueOf(((DynamicResultRow)result.get(0)).get("COMPNAME")));
            publishDepart.setValue("PublishDepart_Name", compName);
            data.setValue("PublishDepart", publishDepart);
         } else {
            AssociationInfo publishDepart = new AssociationInfo();
            publishDepart.setValue("PublishDepart", valueOf(((DynamicResultRow)result.get(0)).get("DEPTID")));
            IDFName compName = (IDFName)valueObjMgr.createDataType();
            compName.setDFName(valueOf(((DynamicResultRow)result.get(0)).get("DEPTNAME")));
            publishDepart.setValue("PublishDepart_Name", compName);
            data.setValue("PublishDepart", publishDepart);
            AssociationInfo mgrDept = new AssociationInfo();
            mgrDept.setValue("MgrDepart", valueOf(((DynamicResultRow)result.get(0)).get("DEPTID")));
            IDFName deptName = (IDFName)valueObjMgr.createDataType();
            deptName.setDFName(valueOf(((DynamicResultRow)result.get(0)).get("DEPTNAME")));
            mgrDept.setValue("MgrDepart_Name", deptName);
            data.setValue("MgrDepart", mgrDept);
         }
      }

   }

   public List<DynamicResultRow> getDept(String userId) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", userId)};
      String sqlSelectOrg = "select a.id,t.name_chs,t.masterledgername,t.abbreviation_chs from bfmasterorganization t,gspuser a where a.sysorgid = t.id and a.id =:ID";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> businessList = this.bqlExecuter.executeSelectStatement(sqlSelectOrg, planIDs, selectParams);
      return businessList;
   }

   private static String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
