package com.inspur.gs.ea.ric.rccssc.specomplimea.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import java.util.Date;

public final class SpeCompliMeaUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static String getCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Code");
   }

   public static void setCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Code", propertyValue);
   }

   public static String getName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Name");
   }

   public static void setName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Name", propertyValue);
   }

   public static String getKeyWord(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "KeyWord");
   }

   public static void setKeyWord(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "KeyWord", propertyValue);
   }

   public static AssoInfoBase getPublishDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "PublishDepart");
   }

   public static void setPublishDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "PublishDepart", propertyValue);
   }

   public static String getAbstract(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Abstract");
   }

   public static void setAbstract(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Abstract", propertyValue);
   }

   public static AssoInfoBase getMgrDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "MgrDepart");
   }

   public static void setMgrDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "MgrDepart", propertyValue);
   }

   public static String getSpeStatus(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "SpeStatus");
   }

   public static void setSpeStatus(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "SpeStatus", propertyValue);
   }

   public static String getRegistrant(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Registrant");
   }

   public static void setRegistrant(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Registrant", propertyValue);
   }

   public static Date getRegistDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "RegistDate");
   }

   public static void setRegistDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "RegistDate", propertyValue);
   }

   public static String getSpecVersion(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "SpecVersion");
   }

   public static void setSpecVersion(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "SpecVersion", propertyValue);
   }

   public static String getRange(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Range");
   }

   public static void setRange(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Range", propertyValue);
   }

   public static String getAbstract01(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Abstract01");
   }

   public static void setAbstract01(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Abstract01", propertyValue);
   }

   public static IBillState getBIllStatus(IEntityData data) {
      return (IBillState)EntityDataUtils.getValue(data, "BIllStatus");
   }

   public static void setBIllStatus(IEntityData data, IBillState propertyValue) {
      EntityDataUtils.setValue(data, "BIllStatus", propertyValue);
   }

   public static IProcessInstance getInstanceID(IEntityData data) {
      return (IProcessInstance)EntityDataUtils.getValue(data, "InstanceID");
   }

   public static void setInstanceID(IEntityData data, IProcessInstance propertyValue) {
      EntityDataUtils.setValue(data, "InstanceID", propertyValue);
   }

   public static IAdministrativeInfo getCreateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "CreateInfo");
   }

   public static void setCreateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "CreateInfo", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static IEntityDataCollection getControlMeasures(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "ControlMeasure");
   }

   public static IEntityDataCollection getSpeCompliMeaFiles(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "SpeCompliMeaFile");
   }
}
