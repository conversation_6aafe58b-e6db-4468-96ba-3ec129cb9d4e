package com.inspur.gs.ea.ric.rccssc.specomplimea.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.api.manager.ICefValueObjManager;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.spi.common.UdtManagerUtil;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.bf.df.dfudt.udt.dfcode.dfcode.entity.IDFCode;
import com.inspur.gs.bf.df.dfudt.udt.dfname.dfname.entity.IDFName;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class SpeCompliMeaDefaultDeptDetermination extends AbstractDetermination {
   private IBqlExecuter bqlExecuter;

   public SpeCompliMeaDefaultDeptDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
      IBqlExecuter iBqlExecuter = this.getBqlExecuter();
      String userid = CAFContext.current.getUserId();
      IDbParameter iDbParameters = this.bqlExecuter.makeInParam("userid", userid);
      List<String> list = new ArrayList();
      list.add("c31ae4d9-8ee1-43ec-a526-09720fb3f4c9");
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String bqlStatement1 = "select a.sysorgid from gspuser a where a.id=:userid";
      List<DynamicResultRow> result1 = iBqlExecuter.executeSelectStatement(bqlStatement1, list, new IDbParameter[]{iDbParameters});
      String orgid = ((DynamicResultRow)result1.get(0)).getValues().get(0).toString();
      IDbParameter iDbParameters1 = this.bqlExecuter.makeInParam("orgid", orgid);
      List<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("2f63f53b-a2e3-4d90-85dd-b2c9c6e58cbd");
      String bqlStatement = "select a.id,a.code,a.name_chs from bfadminorganization a where a.id=:orgid";
      List<DynamicResultRow> result = null;
      result = iBqlExecuter.executeSelectStatement(bqlStatement, refEntityIDs, new IDbParameter[]{iDbParameters1});
      AssociationInfo dutyDeptInfo = new AssociationInfo();
      ICefValueObjManager valueObjMgr = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfcode.DFCode");
      IDFCode code = (IDFCode)valueObjMgr.createDataType();
      ICefValueObjManager valueObjMgr1 = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfname.DFName");
      IDFName name = (IDFName)valueObjMgr1.createDataType();

      for(DynamicResultRow beChild : result) {
         dutyDeptInfo.setValue("MgrDepart", beChild.getValues().get(0).toString());
         code.setDFCode(beChild.getValues().get(1).toString());
         name.setDFName(beChild.getValues().get(2).toString());
      }

      dutyDeptInfo.setValue("MgrDepart_Code", code);
      dutyDeptInfo.setValue("MgrDepart_Name", name);
      data.setValue("MgrDepart", dutyDeptInfo);
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
