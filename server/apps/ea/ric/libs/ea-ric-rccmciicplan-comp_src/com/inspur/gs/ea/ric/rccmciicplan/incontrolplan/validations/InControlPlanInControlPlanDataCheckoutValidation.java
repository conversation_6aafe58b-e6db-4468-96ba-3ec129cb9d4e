package com.inspur.gs.ea.ric.rccmciicplan.incontrolplan.validations;

import com.inspur.edp.bef.api.action.validation.IValidationContext;
import com.inspur.edp.bef.spi.action.validation.AbstractAfterSaveValidation;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;

public class InControlPlanInControlPlanDataCheckoutValidation extends AbstractAfterSaveValidation {
   public InControlPlanInControlPlanDataCheckoutValidation(IValidationContext context, IChangeDetail change) {
      super(context, change);
   }

   public void execute() {
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
