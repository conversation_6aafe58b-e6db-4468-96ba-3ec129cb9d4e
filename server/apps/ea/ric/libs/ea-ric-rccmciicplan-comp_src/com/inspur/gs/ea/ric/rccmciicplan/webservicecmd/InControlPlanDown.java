package com.inspur.gs.ea.ric.rccmciicplan.webservicecmd;

import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

@GspServiceBundle(
   applicationName = "ea",
   serviceUnitName = "ric",
   serviceName = "ric_incontrolservice"
)
public interface InControlPlanDown {
   @RpcServiceMethod(
      serviceId = "com.inspur.gs.ea.ric.rccmciicplan.webservicecmd.InControlPlanDown.InControlPlanDownChange"
   )
   void InControlPlanDownChange(@RpcParam(paramName = "flag") String var1, @RpcParam(paramName = "ID") String var2);
}
