package com.inspur.gs.ea.ric.rccmciicplan.incontrolplan.validations;

import com.inspur.edp.bef.api.action.validation.IValidationContext;
import com.inspur.edp.bef.api.exceptions.BizMessageException;
import com.inspur.edp.bef.spi.action.validation.AbstractValidation;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.api.message.IBizMessage;
import com.inspur.edp.cef.api.message.MessageLevel;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class InControlPlanInControlPlanDeptCheckoutValidation extends AbstractValidation {
   private IBqlExecuter bqlExecuter;

   public InControlPlanInControlPlanDeptCheckoutValidation(IValidationContext context, IChangeDetail change) {
      super(context, change);
   }

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      int ChangeType = this.getChange().getChangeType().getValue();
      if (ChangeType != 2) {
         IEntityData data = this.getData();
         List<String> Deptid = new ArrayList();

         for(IEntityData item : (IEntityDataCollection)data.getChilds().get("InControlOrg")) {
            Deptid.add(((AssociationInfo)item.getValue("DeptId")).getValue("DeptId").toString());
         }

         if (Deptid.size() != 0 && !cheakRepeat(Deptid)) {
            IBizMessage text = this.getContext().createMessageWithLocation(MessageLevel.Error, "部门不能重复！", new String[0]);
            throw new BizMessageException("InControl002", text);
         }
      }

      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "RiskPlanDownType")};
      String sqlsel = "select configvalue from bfbfkvresult where configkey = :code ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> result1 = this.bqlExecuter.executeSelectStatement(sqlsel, planIDs, queryParams);
      String approveProcess = "";
      if (CollectionUtils.isNotEmpty(result1)) {
         approveProcess = ((DynamicResultRow)result1.get(0)).get("configvalue").toString();
      }

      if (approveProcess.equals("ReportLine")) {
         String sqlSelectDept = "";
         ArrayList<String> refEntityIDs1 = new ArrayList();
         IEntityData data = this.getData();

         for(IEntityData item : (IEntityDataCollection)data.getChilds().get("InControlOrg")) {
            IDbParameter[] selectUserParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("sysorgid", ((AssociationInfo)item.getValue("DeptId")).getValue("DeptId").toString())};
            sqlSelectDept = "select t.reportingtoid AS SYSUSER from GSPWFREPORTINGLINEDATA t where t.REPORTINGLINEDEFID=(select t.id from GSPWFREPORTINGLINEDEF t where t.code='RicPlanDown') and t.ORGANIZATIONID=:sysorgid";
            this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            List<DynamicResultRow> users = this.bqlExecuter.executeSelectStatement(sqlSelectDept, refEntityIDs1, selectUserParams);
            if (users.size() == 0) {
               String sqlselect = "select fullpathname_chs from bfadminorganization where id = :sysorgid ";
               this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
               List<DynamicResultRow> fullpathname = this.bqlExecuter.executeSelectStatement(sqlselect, refEntityIDs1, selectUserParams);
               IBizMessage text = this.getContext().createMessageWithLocation(MessageLevel.Error, "请检查" + String.valueOf(((DynamicResultRow)fullpathname.get(0)).getValues().get(0)) + "汇报关系是否设置！", new String[0]);
               throw new BizMessageException("InControl013", text);
            }
         }
      }

   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }

   public static boolean cheakRepeat(List<String> array) {
      HashSet<String> hashSet = new HashSet();

      for(int i = 0; i < array.size(); ++i) {
         hashSet.add(array.get(i));
      }

      return hashSet.size() == array.size();
   }
}
