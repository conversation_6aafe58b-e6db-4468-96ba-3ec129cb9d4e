package com.inspur.gs.ea.ric.rccmciicplan.incontrolplan.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import io.iec.edp.caf.boot.context.CAFContext;
import java.util.Date;

public class InControlPlanShowDateDetermination extends AbstractDetermination {
   public InControlPlanShowDateDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   public void execute() {
      IEntityData inControlPlan = this.getData();
      Date date = Date.from(CAFContext.current.getCurrentDateTime().toInstant());
      inControlPlan.setValue("BeginDate", date);
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
