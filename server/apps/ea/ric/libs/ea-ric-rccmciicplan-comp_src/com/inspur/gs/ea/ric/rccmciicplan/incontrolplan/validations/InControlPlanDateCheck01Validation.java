package com.inspur.gs.ea.ric.rccmciicplan.incontrolplan.validations;

import com.inspur.edp.bef.api.action.validation.IValidationContext;
import com.inspur.edp.bef.api.exceptions.BizMessageException;
import com.inspur.edp.bef.spi.action.validation.AbstractValidation;
import com.inspur.edp.cef.api.message.IBizMessage;
import com.inspur.edp.cef.api.message.MessageLevel;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.gs.ea.ric.rccmciicplan.InControlResultException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class InControlPlanDateCheck01Validation extends AbstractValidation {
   public InControlPlanDateCheck01Validation(IValidationContext context, IChangeDetail change) {
      super(context, change);
   }

   public void execute() {
      IEntityData data = this.getData();
      Date beginDate = (Date)data.getValue("BeginDate");
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
      Date nowDate1 = null;
      Date nowDate2 = null;

      try {
         nowDate1 = sdf.parse(sdf.format(((IAdministrativeInfo)data.getValue("CreateInfo")).getCreatedOn()));
         nowDate2 = sdf.parse(sdf.format(data.getValue("BeginDate")));
      } catch (ParseException e) {
         throw new InControlResultException(e.getMessage());
      }

      if (((Date)data.getValue("EndDate")).before(nowDate2) && !data.getValue("EndDate").equals(nowDate2)) {
         IBizMessage text = this.getContext().createMessageWithLocation(MessageLevel.Error, "结束日期不能早于开始日期！", new String[0]);
         throw new BizMessageException("InControl011", text);
      } else if (beginDate.compareTo(nowDate1) == -1) {
         IBizMessage text = this.getContext().createMessageWithLocation(MessageLevel.Error, "开始日期不能早于创建日期！", new String[0]);
         throw new BizMessageException("InControl012", text);
      }
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
