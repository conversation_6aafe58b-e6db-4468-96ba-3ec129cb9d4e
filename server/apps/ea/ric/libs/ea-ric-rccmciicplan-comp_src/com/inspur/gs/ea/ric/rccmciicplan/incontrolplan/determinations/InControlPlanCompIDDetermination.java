package com.inspur.gs.ea.ric.rccmciicplan.incontrolplan.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.api.exceptions.BefException;
import com.inspur.edp.bef.entity.exception.ExceptionLevel;
import com.inspur.edp.bef.spi.action.determination.AbstractB4SaveDetermination;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;

public class InControlPlanCompIDDetermination extends AbstractB4SaveDetermination {
   private IBqlExecuter bqlExecuter;

   public InControlPlanCompIDDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      WebSession webSession = CAFContext.current.getSession();
      String sysOrgId = webSession.getSysOrgId();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("sysOrgId", sysOrgId)};
      String sqlselect = "select ownerid from bfadminorganization where id = :sysOrgId ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> comp = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      if (comp == null) {
         throw new BefException("002", "未查询到人员组织信息！请检查！", (RuntimeException)null, ExceptionLevel.Info);
      } else {
         String compId = (String)((DynamicResultRow)comp.get(0)).get("ownerid");
         this.getData().setValue("CompanyID", compId);
      }
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
