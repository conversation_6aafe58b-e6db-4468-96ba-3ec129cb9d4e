package com.inspur.gs.ea.ric.rccmciicplan.webservicecmd;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.edp.svc.message.platform.api.ICommonMessageService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.dataaccess.DbType;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.message.api.CAFMessage;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.jpa.TypedParameterValue;
import org.hibernate.type.StandardBasicTypes;

public class InControlPlanDownImp implements InControlPlanDown {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void InControlPlanDownChange(String flag, String ID) {
      String CheckId = UUID.randomUUID().toString();
      if ("Pass".equals(flag)) {
         ArrayList<String> planIDs = new ArrayList();
         IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("code", "RiskPlanDownType")};
         String sqlsel = "select configvalue from bfbfkvresult where configkey = :code ";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         List<DynamicResultRow> result1 = this.bqlExecuter.executeSelectStatement(sqlsel, planIDs, queryParams);
         String approveProcess = "";
         if (CollectionUtils.isNotEmpty(result1)) {
            approveProcess = ((DynamicResultRow)result1.get(0)).get("configvalue").toString();
         }

         IBqlExecuter bqlExecuter = this.getBqlExecuter();
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("b4a25360-88a1-4a9e-b81f-f1b3a5925423");
         IDbParameter iDbParameters = bqlExecuter.makeInParam("parentid", ID);
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
         String bql = "SELECT a.COMPID,a.DEPTID,a.PARENTID,a.PLANNAME,a.PLANCODE,b.BEGINDATE,b.ENDDATE FROM InControlPlan.InControlOrg a left join InControlPlan.InControlPlan b ON a.PARENTID = b.ID WHERE a.PARENTID = @parentid";
         List<DynamicResultRow> result = null;
         result = bqlExecuter.executeSelectStatement(bql, refEntityIDs, new IDbParameter[]{iDbParameters});
         List<String> deptIdList = new ArrayList();
         List<String> planNameList = new ArrayList();

         for(DynamicResultRow row : result) {
            deptIdList.add((String)row.get("DEPTID"));
            planNameList.add((String)row.get("PLANNAME"));
         }

         IBqlExecuter bqlExecuterRep = this.getBqlExecuter();
         List listId = new ArrayList();
         if (result.size() > 0) {
            for(DynamicResultRow beChild : result) {
               String ZID = UUID.randomUUID().toString();
               listId.add(ZID);
               SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
               String date1 = dateFormat.format(beChild.getValues().get(5));
               String date2 = dateFormat.format(beChild.getValues().get(6));
               IDbParameter[] iDbParametersRep = new IDbParameter[]{bqlExecuterRep.makeInParam("ZID", ZID), bqlExecuterRep.makeInParam("ComPanyId", beChild.getValues().get(0).toString()), bqlExecuterRep.makeInParam("DeptId", beChild.getValues().get(1).toString()), bqlExecuterRep.makeInParam("ID", beChild.getValues().get(2).toString()), bqlExecuterRep.makeInParam("PlanName", CAFContext.current.getDbType() == DbType.SQLServer ? new TypedParameterValue(StandardBasicTypes.NSTRING, beChild.getValues().get(3).toString()) : beChild.getValues().get(3).toString()), bqlExecuterRep.makeInParam("PlanCode", CAFContext.current.getDbType() == DbType.SQLServer ? new TypedParameterValue(StandardBasicTypes.NSTRING, beChild.getValues().get(4).toString()) : beChild.getValues().get(4).toString()), bqlExecuterRep.makeInParam("CreatTime", date1), bqlExecuterRep.makeInParam("EndTime", date2), bqlExecuterRep.makeInParam("Bstatus", 0), bqlExecuterRep.makeInParam("status", 0), bqlExecuterRep.makeInParam("UpdateMatrix", 0)};
               String InsertBql = "INSERT INTO EAINCONTROLQUES(ID, COMPANYID, DEPTID, PLANID, PLANCODE, PLANNAME,CREATTIME,ENDTIME,BSTATUS,status,UpdateMatrix)values(:ZID, :ComPanyId,:DeptId,:ID,:PlanCode,:PlanName,:CreatTime,:EndTime, :Bstatus,:status,:UpdateMatrix)";
               bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
               ArrayList<String> refEntityIDsRep = new ArrayList();
               refEntityIDsRep.add("211c8128-e92a-42c5-abaa-8aa54874e53e");
               bqlExecuter.executeBqlStatement(InsertBql, refEntityIDsRep, iDbParametersRep);
               IDbParameter[] iDbParametersRep1 = new IDbParameter[]{bqlExecuterRep.makeInParam("ZID", UUID.randomUUID().toString()), bqlExecuterRep.makeInParam("CompId", beChild.getValues().get(0).toString()), bqlExecuterRep.makeInParam("DeptId", beChild.getValues().get(1).toString()), bqlExecuterRep.makeInParam("PlanId", beChild.getValues().get(2).toString()), bqlExecuterRep.makeInParam("ParentID", CheckId), bqlExecuterRep.makeInParam("Bstatus", 0), bqlExecuterRep.makeInParam("QuesID", ZID)};
               String InsertBql1 = "INSERT INTO EAINCONTROLORGAN(ID, COMPID, DEPTID, PLANID,PARENTID, BSTATUS,QUESID)values(:ZID, :CompId, :DeptId, :PlanId, :ParentID, :Bstatus, :QuesID)";
               bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
               ArrayList<String> refEntityIDsRep1 = new ArrayList();
               refEntityIDsRep1.add("ec6d14c2-5773-48bd-99e4-5771be932c7d");
               bqlExecuter.executeBqlStatement(InsertBql1, refEntityIDsRep1, iDbParametersRep1);
            }
         }

         IBqlExecuter PlanbqlExecuter = this.getBqlExecuter();
         ArrayList<String> InControlPlan = new ArrayList();
         InControlPlan.add("b4a25360-88a1-4a9e-b81f-f1b3a5925423");
         String Planbql = "SELECT PLANCODE,PLANNAME,PLANTARGET,BEGINDATE,ENDDATE,CREATEINFO_CREATEDBY,CREATEINFO_CREATEDON,STATUS,COMPANYID FROM EAINCONTROLPLAN WHERE EAINCONTROLPLAN.ID = @ID";
         IDbParameter PlanParameters = PlanbqlExecuter.makeInParam("ID", ID);
         List<DynamicResultRow> Planresult = null;
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
         Planresult = PlanbqlExecuter.executeSelectStatement(Planbql, InControlPlan, new IDbParameter[]{PlanParameters});
         IBqlExecuter bqlExecuterRep1 = this.getBqlExecuter();
         if (Planresult.size() > 0) {
            for(DynamicResultRow resultRow : Planresult) {
               SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
               String date1 = dateFormat.format(resultRow.getValues().get(6));
               String date2 = dateFormat.format(resultRow.getValues().get(3));
               String date3 = dateFormat.format(resultRow.getValues().get(4));
               IDbParameter[] iDbParametersRep = new IDbParameter[]{bqlExecuterRep1.makeInParam("ZID", CheckId), bqlExecuterRep1.makeInParam("PlanId", ID), bqlExecuterRep1.makeInParam("PlanCode", CAFContext.current.getDbType() == DbType.SQLServer ? new TypedParameterValue(StandardBasicTypes.NSTRING, resultRow.getValues().get(0).toString()) : resultRow.getValues().get(0).toString()), bqlExecuterRep1.makeInParam("PlanName", CAFContext.current.getDbType() == DbType.SQLServer ? new TypedParameterValue(StandardBasicTypes.NSTRING, resultRow.getValues().get(1).toString()) : resultRow.getValues().get(1).toString()), bqlExecuterRep1.makeInParam("PlanTarget", CAFContext.current.getDbType() == DbType.SQLServer ? new TypedParameterValue(StandardBasicTypes.NSTRING, valueOf(resultRow.getValues().get(2))) : valueOf(resultRow.getValues().get(2))), bqlExecuterRep1.makeInParam("BeginDate", date2), bqlExecuterRep1.makeInParam("EndDate", date3), bqlExecuterRep1.makeInParam("CreatPeople", resultRow.getValues().get(5).toString()), bqlExecuterRep1.makeInParam("CreatsTime", date1), bqlExecuterRep1.makeInParam("BillStatus", 2), bqlExecuterRep1.makeInParam("CompanyID", valueOf(resultRow.getValues().get(8)))};
               String InsertBql = "INSERT INTO EAINCONTROLCHECK(ID, PLANID, PLANCODE, PLANNAME, PLANTARGET, BEGINDATE,ENDDATE,CREATPEOPLE,CREATSTIME,BILLSTATUS,COMPANYID)values(:ZID, :PlanId,:PlanCode,:PlanName,:PlanTarget,:BeginDate,:EndDate,:CreatPeople,:CreatsTime,:BillStatus,:CompanyID)";
               PlanbqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
               ArrayList<String> refEntityIDsRep = new ArrayList();
               PlanbqlExecuter.executeBqlStatement(InsertBql, refEntityIDsRep, iDbParametersRep);
            }
         }

         for(int i = 0; i < deptIdList.size(); ++i) {
            IDbParameter[] selectParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("sysorgid", deptIdList.get(i))};
            String sqlSelectDept = "";
            if ("ReportLine".equals(approveProcess)) {
               sqlSelectDept = "select t.reportingtoid AS SYSUSER from GSPWFREPORTINGLINEDATA t where t.REPORTINGLINEDEFID=(select t.id from GSPWFREPORTINGLINEDEF t where t.code='RicPlanDown') and t.ORGANIZATIONID=:sysorgid";
            } else {
               sqlSelectDept = "SELECT B.SYSUSER AS SYSUSER FROM EACOMPLILINKMAN T,BFEMPLOYEESYSUSER B WHERE T.STAFFNAME = B.PARENTID AND T.ORGANIZATION= :sysorgid AND T.STAFFTYPE ='1'";
            }

            bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            List<DynamicResultRow> users = bqlExecuter.executeSelectStatement(sqlSelectDept, planIDs, selectParams);
            ICommonMessageService service = (ICommonMessageService)SpringBeanUtils.getBean(ICommonMessageService.class);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(users)) {
               CAFMessage message = new CAFMessage();
               message.setSender("admin");
               List<String> receivers = new ArrayList();

               for(DynamicResultRow row : users) {
                  receivers.add((String)row.get("SYSUSER"));
               }

               message.setReceivers(receivers);
               message.setSubject("内控计划" + (String)planNameList.get(0) + "问卷填报提醒");
               message.setMsgText("请按时填写下发至本部门" + (String)planNameList.get(0) + "计划的识别问卷！");
               message.setSendType("RicPlanDownAlert");
               service.sendMessage(message);
            }
         }
      }

   }

   public static String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
