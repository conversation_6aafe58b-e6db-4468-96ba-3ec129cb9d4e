package com.inspur.gs.ea.ric.rccmciinconpromanz.webservicecmd;

import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

@GspServiceBundle(
   applicationName = "ea",
   serviceUnitName = "ric",
   serviceName = "gsxmrccm_inconpromanzservice"
)
public interface InConProManZDown {
   @RpcServiceMethod(
      serviceId = "com.inspur.gs.ea.ric.rccmciinconpromanz.webservicecmd.InConProManZDown.InControlPlanDownChange"
   )
   void InControlPlanDownChange(@RpcParam(paramName = "flag") String var1, @RpcParam(paramName = "ID") String var2);
}
