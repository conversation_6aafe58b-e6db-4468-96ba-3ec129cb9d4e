package com.inspur.gs.ea.ric.rccmciinconpromanz.inconpromanz.entityactions;

import com.inspur.edp.bef.api.action.VoidActionResult;
import com.inspur.edp.bef.api.be.IBENodeEntityContext;
import com.inspur.edp.bef.spi.action.RootAbstractAction;
import com.inspur.edp.cef.entity.entity.IEntityData;

public class InConProManZUpdateVersionAction extends RootAbstractAction<VoidActionResult> {
   public InConProManZUpdateVersionAction(IBENodeEntityContext beContext) {
      super(beContext);
   }

   public void execute() {
   }

   private IEntityData getData() {
      return this.getBEContext().getCurrentData();
   }
}
