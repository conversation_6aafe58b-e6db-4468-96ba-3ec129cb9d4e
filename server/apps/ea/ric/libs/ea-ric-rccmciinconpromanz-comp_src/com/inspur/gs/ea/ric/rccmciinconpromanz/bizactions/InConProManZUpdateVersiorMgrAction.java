package com.inspur.gs.ea.ric.rccmciinconpromanz.bizactions;

import com.inspur.edp.bef.api.be.IBEManagerContext;
import com.inspur.edp.bef.api.be.IBEService;
import com.inspur.edp.bef.api.be.IBusinessEntity;
import com.inspur.edp.bef.spi.action.AbstractManagerAction;

public class InConProManZUpdateVersiorMgrAction extends AbstractManagerAction<Boolean> {
   private String id;

   public InConProManZUpdateVersiorMgrAction(IBEManagerContext managerContext, String id) {
      super(managerContext);
      this.id = id;
   }

   public void execute() {
   }

   private IBusinessEntity getEntity(String dataId) {
      return super.getBEManagerContext().getEntity(dataId);
   }

   private IBEService getMgr() {
      return super.getBEManagerContext().getBEManager();
   }
}
