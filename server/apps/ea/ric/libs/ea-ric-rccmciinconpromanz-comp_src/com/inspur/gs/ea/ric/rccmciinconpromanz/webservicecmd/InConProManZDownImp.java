package com.inspur.gs.ea.ric.rccmciinconpromanz.webservicecmd;

import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;

public class InConProManZDownImp implements InConProManZDown {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void InControlPlanDownChange(String flag, String ID) {
      if ("Pass".equals(flag) && ID != null) {
         IBqlExecuter bqlExecuter = this.getBqlExecuter();
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("6a0620ef-bc69-4f56-b3c6-af89da5f14d8");
         IDbParameter[] iDbParameters = new IDbParameter[1];
         iDbParameters[0] = bqlExecuter.makeInParam("CL", ID);
         String bql = "update InConProManZ.InConProManZ  set IsCancel = '0' where ID = @CL";
         bqlExecuter.executeBqlStatement(bql, refEntityIDs, iDbParameters);
      }

   }
}
