package com.inspur.gs.ea.ric.ricclct.complitrainplan_frm.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;

public final class TrainRangeUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }

   public static AssoInfoBase getDepartment(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Department");
   }

   public static void setDepartment(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Department", propertyValue);
   }

   public static AssoInfoBase getCompany(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "Company");
   }

   public static void setCompany(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "Company", propertyValue);
   }

   public static String getProcess(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Process");
   }

   public static void setProcess(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Process", propertyValue);
   }
}
