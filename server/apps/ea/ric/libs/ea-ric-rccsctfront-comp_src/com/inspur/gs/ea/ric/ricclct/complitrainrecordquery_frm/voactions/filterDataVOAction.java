package com.inspur.gs.ea.ric.ricclct.complitrainrecordquery_frm.voactions;

import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import java.util.ArrayList;

public class filterDataVOAction extends BeforeQueryAction {
   public filterDataVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      EntityFilter filter = this.getQueryContext().getFilter();
      ArrayList<FilterCondition> list = new ArrayList();
      FilterCondition filterCondition2 = new FilterCondition(0, "Re", ExpressCompareType.Equal, "Approached", 0, ExpressRelationType.Empty, ExpressValueType.Value);
      list.add(filterCondition2);
      filter.addFilterConditions(list);
   }
}
