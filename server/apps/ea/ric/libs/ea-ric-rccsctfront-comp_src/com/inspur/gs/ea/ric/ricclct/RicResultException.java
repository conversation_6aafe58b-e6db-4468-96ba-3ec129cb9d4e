package com.inspur.gs.ea.ric.ricclct;

import io.iec.edp.caf.commons.exception.CAFRuntimeException;
import io.iec.edp.caf.commons.exception.ExceptionLevel;

public class RicResultException extends CAFRuntimeException {
   public RicResultException(String serviceUnitCode, String exceptionCode, String message, Exception innerException, ExceptionLevel level, boolean bizException) {
      super(serviceUnitCode, exceptionCode, message, innerException, level, bizException);
   }
}
