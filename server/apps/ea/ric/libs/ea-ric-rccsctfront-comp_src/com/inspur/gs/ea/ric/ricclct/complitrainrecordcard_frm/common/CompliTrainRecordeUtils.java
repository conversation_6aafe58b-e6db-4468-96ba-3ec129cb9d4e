package com.inspur.gs.ea.ric.ricclct.complitrainrecordcard_frm.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import java.util.Date;

public final class CompliTrainRecordeUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static AssoInfoBase getTrainPlan(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "TrainPlan");
   }

   public static void setTrainPlan(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "TrainPlan", propertyValue);
   }

   public static Date getTrainDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "TrainDate");
   }

   public static void setTrainDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "TrainDate", propertyValue);
   }

   public static String getTrainContent(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainContent");
   }

   public static void setTrainContent(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainContent", propertyValue);
   }

   public static String getTrainGain(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainGain");
   }

   public static void setTrainGain(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainGain", propertyValue);
   }

   public static String getTrainWay(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainWay");
   }

   public static void setTrainWay(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainWay", propertyValue);
   }

   public static String getTrainPlace(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainPlace");
   }

   public static void setTrainPlace(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainPlace", propertyValue);
   }

   public static String getReDepart(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ReDepart");
   }

   public static void setReDepart(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ReDepart", propertyValue);
   }

   public static IAdministrativeInfo getCreateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "CreateInfo");
   }

   public static void setCreateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "CreateInfo", propertyValue);
   }

   public static String getRe(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Re");
   }

   public static void setRe(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Re", propertyValue);
   }

   public static String getParticipant(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Participant");
   }

   public static void setParticipant(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Participant", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static String getEXT6(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT6");
   }

   public static void setEXT6(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT6", propertyValue);
   }

   public static String getEXT7(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT7");
   }

   public static void setEXT7(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT7", propertyValue);
   }

   public static String getEXT8(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT8");
   }

   public static void setEXT8(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT8", propertyValue);
   }

   public static String getEXT9(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT9");
   }

   public static void setEXT9(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT9", propertyValue);
   }

   public static String getEXT10(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT10");
   }

   public static void setEXT10(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT10", propertyValue);
   }
}
