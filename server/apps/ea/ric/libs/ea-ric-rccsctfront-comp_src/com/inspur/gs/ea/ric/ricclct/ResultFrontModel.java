package com.inspur.gs.ea.ric.ricclct;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ResultFrontModel {
   @JsonProperty("code")
   public String code = "ok";
   @JsonProperty("data")
   public Object data;
   @JsonProperty("mes")
   public String mes;
   @JsonProperty("stack")
   public String stack;

   public ResultFrontModel(Object data) {
      this.data = data;
   }

   public ResultFrontModel(String code, String mes) {
      this.code = code;
      this.mes = mes;
   }

   public ResultFrontModel(String code, String mes, Object data) {
      this.code = code;
      this.mes = mes;
      this.data = data;
   }

   public ResultFrontModel() {
   }

   public static ResultFrontModel getResult(Object data) {
      return new ResultFrontModel(data);
   }

   public static ResultFrontModel getResult(String code, String mes) {
      return new ResultFrontModel(code, mes);
   }

   public static ResultFrontModel getResult(String code, String mes, Object data) {
      return new ResultFrontModel(code, mes, data);
   }

   public ResultFrontModel setCode(String code) {
      this.code = code;
      return this;
   }

   public ResultFrontModel setMessage(String mes) {
      this.mes = mes;
      return this;
   }

   public ResultFrontModel setStackTrace(String stack) {
      this.stack = stack;
      return this;
   }

   public ResultFrontModel setMes(String mes) {
      this.mes = mes;
      return this;
   }

   public ResultFrontModel setStack(String stack) {
      this.stack = stack;
      return this;
   }
}
