package com.inspur.gs.ea.ric.ricclct.complitrainplan_frm.voactions;

import com.inspur.edp.bef.api.exceptions.BefException;
import com.inspur.edp.bef.entity.exception.ExceptionLevel;
import com.inspur.edp.bff.api.manager.context.QueryContext;
import com.inspur.edp.bff.spi.action.query.BeforeQueryAction;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.entity.condition.EntityFilter;
import com.inspur.edp.cef.entity.condition.ExpressCompareType;
import com.inspur.edp.cef.entity.condition.ExpressRelationType;
import com.inspur.edp.cef.entity.condition.ExpressValueType;
import com.inspur.edp.cef.entity.condition.FilterCondition;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class BeforeQueryVOAction extends BeforeQueryAction {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public BeforeQueryVOAction(QueryContext context) {
      super(context);
   }

   public void execute() {
      WebSession webSession = CAFContext.current.getSession();
      String userId = webSession.getUserId();
      String sysOrgId = "";
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParamorg = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("userId", userId)};
      String sqlselectorg = "select sysorgid from gspuser where id = :userId ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> orgs = this.bqlExecuter.executeSelectStatement(sqlselectorg, planIDs, queryParamorg);
      if (CollectionUtils.isNotEmpty(orgs)) {
         sysOrgId = this.valueOf(((DynamicResultRow)orgs.get(0)).get("sysorgid"));
         EntityFilter filter = this.getQueryContext().getFilter();
         ArrayList<FilterCondition> list = new ArrayList();
         FilterCondition filterCondition = new FilterCondition(0, "DeptId", ExpressCompareType.Equal, sysOrgId, 0, ExpressRelationType.Empty, ExpressValueType.Value);
         list.add(filterCondition);
         filter.addFilterConditions(list);
      } else {
         throw new BefException("002", "未查询到人员组织信息！请检查！", (RuntimeException)null, ExceptionLevel.Info);
      }
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
