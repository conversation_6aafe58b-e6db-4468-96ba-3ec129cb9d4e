package com.inspur.gs.ea.ric.ricclct.complitrainrecordlist_frm.voactions;

import com.inspur.edp.bef.api.lcp.ILcpFactory;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;

public class updateSubVOAction extends AbstractFSAction<Boolean> {
   private String id;

   public updateSubVOAction(String id) {
      this.id = id;
   }

   public void execute() {
      ModifyChangeDetail change = new ModifyChangeDetail(this.id);
      new ValueObjModifyChangeDetail();
      change.getPropertyChanges().put("Re", "Approached");
      IStandardLcp lcp = ((ILcpFactory)SpringBeanUtils.getBean(ILcpFactory.class)).createLcp("com.inspur.gs.ea.ric.ricclct.CompliTrainRecord");
      lcp.modify(change);
      this.setResult(true);
   }
}
