package com.inspur.gs.ea.ric.ricclct.complitrainrecordcard_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import com.inspur.gs.ea.ric.ricclct.ResultFrontModel;
import com.inspur.gs.ea.ric.ricclct.RicResultException;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.exception.CAFRuntimeException;
import io.iec.edp.caf.commons.exception.ExceptionLevel;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OpenTrainFileVOAction extends AbstractFSAction<String> {
   private Map param;
   private static final Logger LOG = LoggerFactory.getLogger(OpenTrainFileVOAction.class);

   public OpenTrainFileVOAction(Map param) {
      this.param = param;
   }

   public void execute() {
      String MKID = this.valueOf(this.param.get("MKID"));
      String appID = this.valueOf(this.param.get("appID"));
      String sourceSys = this.valueOf(this.param.get("SourceSys"));
      String billCATEGORY = this.valueOf(this.param.get("BillCATEGORY"));
      String billType = this.valueOf(this.param.get("BillType"));
      String billTypeID = this.valueOf(this.param.get("BillTypeID"));
      String billNM = this.valueOf(this.param.get("BillNM"));
      String billCODE = this.valueOf(this.param.get("BillCODE"));
      String DWBH = this.valueOf(this.param.get("DWBH"));
      String operation = this.valueOf(this.param.get("OPERATION"));
      String custom = this.valueOf(this.param.get("custom"));
      Map<String, Object> paramMap = new HashMap();
      paramMap.put("MKID", MKID);
      paramMap.put("appID", appID);
      paramMap.put("SourceSys", sourceSys);
      paramMap.put("BillCATEGORY", billCATEGORY);
      paramMap.put("BillType", billType);
      paramMap.put("BillTypeID", billTypeID);
      paramMap.put("BillNM", billNM);
      paramMap.put("BillCODE", billCODE);
      paramMap.put("DWBH", DWBH);
      paramMap.put("OPERATION", operation);
      paramMap.put("USERCODE", CAFContext.current.getSession().getUserCode());
      Map<String, String> btnMap = new HashMap();
      btnMap.put("btnUpload", "1");
      if ("1".equals(custom)) {
         paramMap.put("OPERATION", "custom");
         paramMap.put("btnArr", btnMap);
      }

      LinkedHashMap<String, Object> infos = new LinkedHashMap();
      infos.put("rpcUrlMapParam", paramMap);

      try {
         RpcClient rpcClient = (RpcClient)SpringBeanUtils.getBean(RpcClient.class);
         ResultFrontModel result = (ResultFrontModel)rpcClient.invoke(ResultFrontModel.class, "com.inspur.gs.eis.image.restful.eis.ImageAPIEisRest.getyxurlmap", "EIS", infos, (HashMap)null);
         String resultstr = JSONSerializer.serialize(result);
         this.setResult(resultstr);
      } catch (CAFRuntimeException e) {
         LOG.error("openEisPage异常 " + e.getMessage());
         throw new RicResultException("RIC", "RIC-ERROR-1001", e.getMessage(), e, ExceptionLevel.Error, true);
      }
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
