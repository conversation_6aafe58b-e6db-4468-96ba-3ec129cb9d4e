package com.inspur.gs.ea.ric.ricclct.complitrainplan.common;

import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.udt.attachmentinfo.attachmentinfo.entity.IAttachmentInfo;
import java.util.Date;

public final class TrainPlanFileUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static String getParentID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParentID");
   }

   public static void setParentID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParentID", propertyValue);
   }

   public static IAttachmentInfo getFileInfo(IEntityData data) {
      return (IAttachmentInfo)EntityDataUtils.getValue(data, "FileInfo");
   }

   public static void setFileInfo(IEntityData data, IAttachmentInfo propertyValue) {
      EntityDataUtils.setValue(data, "FileInfo", propertyValue);
   }

   public static String getFILENAME(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "FILENAME");
   }

   public static void setFILENAME(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "FILENAME", propertyValue);
   }

   public static String getFILEEXT(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "FILEEXT");
   }

   public static void setFILEEXT(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "FILEEXT", propertyValue);
   }

   public static String getFILEID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "FILEID");
   }

   public static void setFILEID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "FILEID", propertyValue);
   }

   public static String getFILESIZE(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "FILESIZE");
   }

   public static void setFILESIZE(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "FILESIZE", propertyValue);
   }

   public static String getUPLOADER(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "UPLOADER");
   }

   public static void setUPLOADER(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "UPLOADER", propertyValue);
   }

   public static Date getUPLOADDATE(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "UPLOADDATE");
   }

   public static void setUPLOADDATE(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "UPLOADDATE", propertyValue);
   }
}
