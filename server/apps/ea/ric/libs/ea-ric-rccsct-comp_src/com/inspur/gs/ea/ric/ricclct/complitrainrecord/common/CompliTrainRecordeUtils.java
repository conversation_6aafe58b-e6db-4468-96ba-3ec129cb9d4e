package com.inspur.gs.ea.ric.ricclct.complitrainrecord.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import java.util.Date;

public final class CompliTrainRecordeUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static AssoInfoBase getTrainPlan(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "TrainPlan");
   }

   public static void setTrainPlan(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "TrainPlan", propertyValue);
   }

   public static Date getTrainDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "TrainDate");
   }

   public static void setTrainDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "TrainDate", propertyValue);
   }

   public static String getTrainContent(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainContent");
   }

   public static void setTrainContent(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainContent", propertyValue);
   }

   public static String getTrainGain(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainGain");
   }

   public static void setTrainGain(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainGain", propertyValue);
   }

   public static String getTrainWay(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainWay");
   }

   public static void setTrainWay(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainWay", propertyValue);
   }

   public static String getTrainPlace(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "TrainPlace");
   }

   public static void setTrainPlace(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "TrainPlace", propertyValue);
   }

   public static AssoInfoBase getReDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "ReDepart");
   }

   public static void setReDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "ReDepart", propertyValue);
   }

   public static IAdministrativeInfo getCreateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "CreateInfo");
   }

   public static void setCreateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "CreateInfo", propertyValue);
   }

   public static String getRe(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Re");
   }

   public static void setRe(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Re", propertyValue);
   }

   public static String getParticipant(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Participant");
   }

   public static void setParticipant(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Participant", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static String getEXT6(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT6");
   }

   public static void setEXT6(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT6", propertyValue);
   }

   public static String getEXT7(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT7");
   }

   public static void setEXT7(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT7", propertyValue);
   }

   public static String getEXT8(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT8");
   }

   public static void setEXT8(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT8", propertyValue);
   }

   public static String getEXT9(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT9");
   }

   public static void setEXT9(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT9", propertyValue);
   }

   public static String getEXT10(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT10");
   }

   public static void setEXT10(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT10", propertyValue);
   }

   public static String getEXT11(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT11");
   }

   public static void setEXT11(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT11", propertyValue);
   }

   public static String getEXT12(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT12");
   }

   public static void setEXT12(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT12", propertyValue);
   }

   public static String getEXT13(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT13");
   }

   public static void setEXT13(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT13", propertyValue);
   }

   public static String getEXT14(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT14");
   }

   public static void setEXT14(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT14", propertyValue);
   }

   public static String getEXT15(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT15");
   }

   public static void setEXT15(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT15", propertyValue);
   }

   public static String getEXT16(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT16");
   }

   public static void setEXT16(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT16", propertyValue);
   }

   public static String getEXT17(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT17");
   }

   public static void setEXT17(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT17", propertyValue);
   }

   public static String getEXT18(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT18");
   }

   public static void setEXT18(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT18", propertyValue);
   }

   public static String getEXT19(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT19");
   }

   public static void setEXT19(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT19", propertyValue);
   }

   public static String getEXT20(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT20");
   }

   public static void setEXT20(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT20", propertyValue);
   }

   public static String getEXT21(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT21");
   }

   public static void setEXT21(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT21", propertyValue);
   }

   public static String getEXT22(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT22");
   }

   public static void setEXT22(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT22", propertyValue);
   }

   public static String getEXT23(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT23");
   }

   public static void setEXT23(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT23", propertyValue);
   }

   public static String getEXT24(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT24");
   }

   public static void setEXT24(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT24", propertyValue);
   }

   public static String getEXT25(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT25");
   }

   public static void setEXT25(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT25", propertyValue);
   }

   public static AssoInfoBase getReCompany(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "ReCompany");
   }

   public static void setReCompany(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "ReCompany", propertyValue);
   }

   public static Date getBeginDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "BeginDate");
   }

   public static void setBeginDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "BeginDate", propertyValue);
   }

   public static Date getEndDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "EndDate");
   }

   public static void setEndDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "EndDate", propertyValue);
   }

   public static Integer getPersonNum(IEntityData data) {
      return (Integer)EntityDataUtils.getValue(data, "PersonNum");
   }

   public static void setPersonNum(IEntityData data, Integer propertyValue) {
      EntityDataUtils.setValue(data, "PersonNum", propertyValue);
   }

   public static String getCultureType(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "CultureType");
   }

   public static void setCultureType(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "CultureType", propertyValue);
   }

   public static String getSECLEVEL(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "SECLEVEL");
   }

   public static void setSECLEVEL(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "SECLEVEL", propertyValue);
   }

   public static String getCreatedBy(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "CreatedBy");
   }

   public static void setCreatedBy(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "CreatedBy", propertyValue);
   }

   public static String getParticipantName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ParticipantName");
   }

   public static void setParticipantName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ParticipantName", propertyValue);
   }

   public static IEntityDataCollection getTrainRecordeFiles(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "TrainRecordeFile");
   }
}
