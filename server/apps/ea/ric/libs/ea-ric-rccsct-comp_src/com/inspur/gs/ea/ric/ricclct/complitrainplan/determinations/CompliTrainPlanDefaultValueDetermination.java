package com.inspur.gs.ea.ric.ricclct.complitrainplan.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;

public class CompliTrainPlanDefaultValueDetermination extends AbstractDetermination {
   public CompliTrainPlanDefaultValueDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   public void execute() {
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
