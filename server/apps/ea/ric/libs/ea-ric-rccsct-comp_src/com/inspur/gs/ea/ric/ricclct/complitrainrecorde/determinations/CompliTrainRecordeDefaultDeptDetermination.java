package com.inspur.gs.ea.ric.ricclct.complitrainrecorde.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.api.exceptions.BefException;
import com.inspur.edp.bef.entity.exception.ExceptionLevel;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cef.api.manager.ICefValueObjManager;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.spi.common.UdtManagerUtil;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.bf.df.dfudt.udt.dfname.dfname.entity.IDFName;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class CompliTrainRecordeDefaultDeptDetermination extends AbstractDetermination {
   private IBqlExecuter bqlExecuter;

   public CompliTrainRecordeDefaultDeptDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      ArrayList<String> planIDs = new ArrayList();
      IEntityData data = this.getData();
      WebSession webSession = CAFContext.current.getSession();
      String sysOrgId = "";
      String userId = webSession.getUserId();
      IDbParameter[] queryParamorg = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("userId", userId)};
      String sqlselectorg = "select sysorgid from gspuser where id = :userId ";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> orgs = this.bqlExecuter.executeSelectStatement(sqlselectorg, planIDs, queryParamorg);
      if (CollectionUtils.isNotEmpty(orgs)) {
         sysOrgId = this.valueOf(((DynamicResultRow)orgs.get(0)).get("sysorgid"));
         AssociationInfo companyIDInfo = (AssociationInfo)data.getValue("ReCompany");
         if (StringUtils.isEmpty(this.valueOf(companyIDInfo.getValue("ReCompany")))) {
            IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("sysOrgId", sysOrgId)};
            String sqlselect = "select ownerid from bfadminorganization where id = :sysOrgId ";
            this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            List<DynamicResultRow> comp = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
            String compId = "";
            if (CollectionUtils.isNotEmpty(comp) && !"".equals(this.valueOf(((DynamicResultRow)comp.get(0)).get("ownerid")))) {
               AssociationInfo deptInfo = new AssociationInfo();
               deptInfo.setValue("ReDepart", sysOrgId);
               ICefValueObjManager unitValueObjMgr = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfname.DFName");
               IDFName name = (IDFName)unitValueObjMgr.createDataType();
               name.setDFName(this.getOrgName(sysOrgId));
               deptInfo.setValue("ReDepart_Name", name);
               data.setValue("ReDepart", deptInfo);
               compId = this.valueOf(((DynamicResultRow)comp.get(0)).get("ownerid"));
            }

            if ("".equals(compId)) {
               compId = sysOrgId;
            }

            AssociationInfo cpmpanyInfo = new AssociationInfo();
            cpmpanyInfo.setValue("ReCompany", compId);
            ICefValueObjManager unitValueObjMgr = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfname.DFName");
            IDFName name = (IDFName)unitValueObjMgr.createDataType();
            name.setDFName(this.getOrgName(compId));
            cpmpanyInfo.setValue("ReCompany_Name", name);
            data.setValue("ReCompany", cpmpanyInfo);
         }

      } else {
         throw new BefException("002", "未查询到人员组织信息！请检查！", (RuntimeException)null, ExceptionLevel.Info);
      }
   }

   private String getOrgName(String id) {
      this.getBqlExecuter();
      List refEntityIDs = new ArrayList();
      String orgName = "";
      IDbParameter iDbParameters = this.bqlExecuter.makeInParam("id", id);
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String bqlStatement = "select name_chs FROM bfadminorganization where id = :id";
      List<DynamicResultRow> result = this.bqlExecuter.executeSelectStatement(bqlStatement, refEntityIDs, new IDbParameter[]{iDbParameters});
      if (CollectionUtils.isNotEmpty(result)) {
         orgName = ((DynamicResultRow)result.get(0)).get("name_chs").toString();
      }

      return orgName;
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
