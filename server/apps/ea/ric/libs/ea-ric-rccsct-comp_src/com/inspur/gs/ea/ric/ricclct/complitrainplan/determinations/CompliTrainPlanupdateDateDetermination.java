package com.inspur.gs.ea.ric.ricclct.complitrainplan.determinations;

import com.inspur.edp.bef.api.action.determination.IDeterminationContext;
import com.inspur.edp.bef.spi.action.determination.AbstractDetermination;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CompliTrainPlanupdateDateDetermination extends AbstractDetermination {
   private static final Logger log = LoggerFactory.getLogger(CompliTrainPlanupdateDateDetermination.class);

   public CompliTrainPlanupdateDateDetermination(IDeterminationContext context, IChangeDetail change) {
      super(context, change);
   }

   public void execute() {
      IEntityData data = this.getData();
      IBillState billState = (IBillState)data.getValue("BIllStatus");
      if (2 == billState.getBillState().getValue()) {
         SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd");
         String date = df.format(new Date());
         SimpleDateFormat df2 = new SimpleDateFormat("yyyy/MM/dd");

         try {
            Date date1 = df2.parse(date);
            data.setValue("ApproveDate", date1);
         } catch (ParseException e) {
            log.error(e.getMessage(), e);
         }
      }

   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
