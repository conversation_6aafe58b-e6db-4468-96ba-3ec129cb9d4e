package com.inspur.gs.ea.ric.ricclct.complitrainrecord.common;

import com.inspur.edp.bef.api.BefRtBeanUtil;
import com.inspur.edp.bef.api.be.MgrActionUtils;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.component.attachment.AttachInfo;

public final class CompliTrainRecordMgrActionUtils {
   public static IEntityData BatchUploadAttachment(AttachInfo batchUploadInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.ricclct.CompliTrainRecord");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "BatchUploadAttachment", new Object[]{batchUploadInfo});
   }

   public static IEntityData UpdateAttachment(AttachInfo updateAttachInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.ricclct.CompliTrainRecord");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "UpdateAttachment", new Object[]{updateAttachInfo});
   }
}
