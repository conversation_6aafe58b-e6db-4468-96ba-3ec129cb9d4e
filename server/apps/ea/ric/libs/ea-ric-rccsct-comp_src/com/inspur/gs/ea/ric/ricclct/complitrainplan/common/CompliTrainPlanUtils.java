package com.inspur.gs.ea.ric.ricclct.complitrainplan.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import java.util.Date;

public final class CompliTrainPlanUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static String getPlanCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "PlanCode");
   }

   public static void setPlanCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "PlanCode", propertyValue);
   }

   public static String getPlanName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "PlanName");
   }

   public static void setPlanName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "PlanName", propertyValue);
   }

   public static String getPlanTarget(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "PlanTarget");
   }

   public static void setPlanTarget(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "PlanTarget", propertyValue);
   }

   public static Date getBeginDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "BeginDate");
   }

   public static void setBeginDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "BeginDate", propertyValue);
   }

   public static Date getEndDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "EndDate");
   }

   public static void setEndDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "EndDate", propertyValue);
   }

   public static Date getApproveDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "ApproveDate");
   }

   public static void setApproveDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "ApproveDate", propertyValue);
   }

   public static String getPlanStatus(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "PlanStatus");
   }

   public static void setPlanStatus(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "PlanStatus", propertyValue);
   }

   public static String getFounder(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Founder");
   }

   public static void setFounder(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Founder", propertyValue);
   }

   public static Date getFoundDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "FoundDate");
   }

   public static void setFoundDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "FoundDate", propertyValue);
   }

   public static String getPlanTarget01(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "PlanTarget01");
   }

   public static void setPlanTarget01(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "PlanTarget01", propertyValue);
   }

   public static IBillState getBIllStatus(IEntityData data) {
      return (IBillState)EntityDataUtils.getValue(data, "BIllStatus");
   }

   public static void setBIllStatus(IEntityData data, IBillState propertyValue) {
      EntityDataUtils.setValue(data, "BIllStatus", propertyValue);
   }

   public static IProcessInstance getInstanceID(IEntityData data) {
      return (IProcessInstance)EntityDataUtils.getValue(data, "InstanceID");
   }

   public static void setInstanceID(IEntityData data, IProcessInstance propertyValue) {
      EntityDataUtils.setValue(data, "InstanceID", propertyValue);
   }

   public static IAdministrativeInfo getCreateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "CreateInfo");
   }

   public static void setCreateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "CreateInfo", propertyValue);
   }

   public static AssoInfoBase getCompanyId(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "CompanyId");
   }

   public static void setCompanyId(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "CompanyId", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static String getEXT6(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT6");
   }

   public static void setEXT6(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT6", propertyValue);
   }

   public static String getEXT7(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT7");
   }

   public static void setEXT7(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT7", propertyValue);
   }

   public static String getEXT8(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT8");
   }

   public static void setEXT8(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT8", propertyValue);
   }

   public static String getEXT9(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT9");
   }

   public static void setEXT9(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT9", propertyValue);
   }

   public static String getEXT10(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT10");
   }

   public static void setEXT10(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT10", propertyValue);
   }

   public static String getEXT11(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT11");
   }

   public static void setEXT11(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT11", propertyValue);
   }

   public static String getEXT12(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT12");
   }

   public static void setEXT12(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT12", propertyValue);
   }

   public static String getEXT13(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT13");
   }

   public static void setEXT13(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT13", propertyValue);
   }

   public static String getEXT14(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT14");
   }

   public static void setEXT14(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT14", propertyValue);
   }

   public static String getEXT15(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT15");
   }

   public static void setEXT15(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT15", propertyValue);
   }

   public static String getEXT16(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT16");
   }

   public static void setEXT16(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT16", propertyValue);
   }

   public static String getEXT17(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT17");
   }

   public static void setEXT17(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT17", propertyValue);
   }

   public static String getEXT18(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT18");
   }

   public static void setEXT18(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT18", propertyValue);
   }

   public static String getEXT19(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT19");
   }

   public static void setEXT19(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT19", propertyValue);
   }

   public static String getEXT20(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT20");
   }

   public static void setEXT20(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT20", propertyValue);
   }

   public static AssoInfoBase getDeptId(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "DeptId");
   }

   public static void setDeptId(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "DeptId", propertyValue);
   }

   public static String getSECLEVEL(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "SECLEVEL");
   }

   public static void setSECLEVEL(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "SECLEVEL", propertyValue);
   }

   public static IEntityDataCollection getTrainRanges(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "TrainRange");
   }

   public static IEntityDataCollection getTrainPlanFiles(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "TrainPlanFile");
   }
}
