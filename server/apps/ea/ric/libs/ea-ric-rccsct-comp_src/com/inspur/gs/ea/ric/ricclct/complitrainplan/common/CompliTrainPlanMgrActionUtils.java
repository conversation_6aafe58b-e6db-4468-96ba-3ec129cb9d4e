package com.inspur.gs.ea.ric.ricclct.complitrainplan.common;

import com.inspur.edp.bef.api.BefRtBeanUtil;
import com.inspur.edp.bef.api.be.MgrActionUtils;
import com.inspur.edp.bef.api.lcp.IStandardLcp;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.common.component.attachment.AttachInfo;

public final class CompliTrainPlanMgrActionUtils {
   public static void UpdateProcInstIdAndState(String bizInstID, String processInstanceID, String submitOrCancel) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.ricclct.CompliTrainPlan");
      MgrActionUtils.executeCustomAction(lcp, "UpdateProcInstIdAndState", new Object[]{bizInstID, processInstanceID, submitOrCancel});
   }

   public static IEntityData BatchUploadAttachment(AttachInfo batchUploadInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.ricclct.CompliTrainPlan");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "BatchUploadAttachment", new Object[]{batchUploadInfo});
   }

   public static IEntityData UpdateAttachment(AttachInfo updateAttachInfo) {
      IStandardLcp lcp = BefRtBeanUtil.getLcpFactory().createLcp("com.inspur.gs.ea.ric.ricclct.CompliTrainPlan");
      return (IEntityData)MgrActionUtils.executeCustomAction(lcp, "UpdateAttachment", new Object[]{updateAttachInfo});
   }
}
