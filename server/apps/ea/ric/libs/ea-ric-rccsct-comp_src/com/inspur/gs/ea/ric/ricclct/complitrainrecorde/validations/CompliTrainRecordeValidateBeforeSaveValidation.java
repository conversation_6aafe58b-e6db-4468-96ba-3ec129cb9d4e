package com.inspur.gs.ea.ric.ricclct.complitrainrecorde.validations;

import com.inspur.edp.bef.api.action.validation.IValidationContext;
import com.inspur.edp.bef.spi.action.validation.AbstractValidation;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;

public class CompliTrainRecordeValidateBeforeSaveValidation extends AbstractValidation {
   private IBqlExecuter bqlExecuter;

   public CompliTrainRecordeValidateBeforeSaveValidation(IValidationContext context, IChangeDetail change) {
      super(context, change);
   }

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void execute() {
      IEntityData data = this.getData();
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
