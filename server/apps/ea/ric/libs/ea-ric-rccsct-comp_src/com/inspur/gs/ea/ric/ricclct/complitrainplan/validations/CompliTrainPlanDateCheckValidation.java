package com.inspur.gs.ea.ric.ricclct.complitrainplan.validations;

import com.inspur.edp.bef.api.action.validation.IValidationContext;
import com.inspur.edp.bef.api.exceptions.BizMessageException;
import com.inspur.edp.bef.spi.action.validation.AbstractValidation;
import com.inspur.edp.cef.api.message.IBizMessage;
import com.inspur.edp.cef.api.message.MessageLevel;
import com.inspur.edp.cef.entity.changeset.IChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import java.util.Date;

public class CompliTrainPlanDateCheckValidation extends AbstractValidation {
   public CompliTrainPlanDateCheckValidation(IValidationContext context, IChangeDetail change) {
      super(context, change);
   }

   public void execute() {
      IEntityData data = this.getData();
      Date beginDate = (Date)data.getValue("BeginDate");
      if (((Date)data.getValue("EndDate")).before(beginDate)) {
         IBizMessage text = this.getContext().createMessageWithLocation(MessageLevel.Error, "结束日期不能早于开始日期！", new String[0]);
         throw new BizMessageException("InControlElvPlan001", text);
      }
   }

   private IEntityData getData() {
      return super.getContext().getCurrentData();
   }
}
