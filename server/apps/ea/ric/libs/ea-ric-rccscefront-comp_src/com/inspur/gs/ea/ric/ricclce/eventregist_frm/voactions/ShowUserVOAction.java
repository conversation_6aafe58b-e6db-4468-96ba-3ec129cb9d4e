package com.inspur.gs.ea.ric.ricclce.eventregist_frm.voactions;

import com.inspur.edp.bff.api.manager.context.RetrieveDefaultContext;
import com.inspur.edp.bff.spi.action.retrievedefault.AfterRetrieveDefaultAction;
import com.inspur.edp.cef.entity.changeset.ModifyChangeDetail;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.cef.entity.entity.IEntityData;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import java.util.Date;

public class ShowUserVOAction extends AfterRetrieveDefaultAction {
   public ShowUserVOAction(RetrieveDefaultContext context) {
      super(context);
   }

   public void execute() {
      WebSession webSession = CAFContext.current.getSession();
      String userCode = webSession.getUserCode();
      String userId = webSession.getUserId();
      String userName = webSession.getUserName();
      IEntityData inConProMan = this.getRetrieveDefaultContext().getRetrieveDefaultResult();
      String id = inConProMan.getID();
      ModifyChangeDetail change = new ModifyChangeDetail(id);
      ValueObjModifyChangeDetail updateInfoChange = new ValueObjModifyChangeDetail();
      updateInfoChange.getPropertyChanges().put("CreatedBy", userName);
      updateInfoChange.getPropertyChanges().put("CreatedOn", new Date());
      change.getPropertyChanges().put("CreateInfo", updateInfoChange);
      this.getLcp().modify(change);
   }
}
