package com.inspur.gs.ea.ric.ricclce.commoncmps;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.edp.cef.api.manager.ICefValueObjManager;
import com.inspur.edp.cef.entity.changeset.ValueObjModifyChangeDetail;
import com.inspur.edp.cef.spi.common.UdtManagerUtil;
import com.inspur.edp.commonmodel.engine.api.data.AssociationInfo;
import com.inspur.edp.dxc.entity.data.DxcCommonData;
import com.inspur.edp.dxc.spi.components.BaseCustomValidateComponent;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.bf.df.dfudt.udt.dfcode.dfcode.entity.IDFCode;
import com.inspur.gs.bf.df.dfudt.udt.dfname.dfname.entity.IDFName;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

@GspComponent("bbd84d95-59ad-4e6a-8ae8-3520032f57ed")
public class EventImport extends BaseCustomValidateComponent {
   private IBqlExecuter bqlExecuter;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void importData() {
      WebSession webSession = CAFContext.current.getSession();
      String userName = webSession.getUserName();
      String userId = webSession.getUserId();
      String orgId = "";
      String orgName = "";
      String orgCode = "";
      List<String> refEntityIDs = new ArrayList();
      IDbParameter[] queryOrgId = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("userid", userId)};
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String sqlOrgId = "select sysorgid from gspuser where id=:userid";
      List<DynamicResultRow> orgIdResult = this.bqlExecuter.executeSelectStatement(sqlOrgId, refEntityIDs, queryOrgId);
      if (CollectionUtils.isNotEmpty(orgIdResult)) {
         IDbParameter[] queryOrgName = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("orgid", ((DynamicResultRow)orgIdResult.get(0)).get("sysorgid"))};
         String sqlOrgName = "select id,code,name_chs from bfadminorganization where id=:orgid";
         List<DynamicResultRow> orgNameResult = this.bqlExecuter.executeSelectStatement(sqlOrgName, refEntityIDs, queryOrgName);
         if (CollectionUtils.isNotEmpty(orgNameResult)) {
            orgId = this.valueOf(((DynamicResultRow)orgNameResult.get(0)).get("id"));
            orgName = this.valueOf(((DynamicResultRow)orgNameResult.get(0)).get("name_chs"));
            orgCode = this.valueOf(((DynamicResultRow)orgNameResult.get(0)).get("code"));
         }
      }

      ICefValueObjManager codeValue = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfcode.DFCode");
      IDFCode code = (IDFCode)codeValue.createDataType();
      code.setDFCode(orgCode);
      ICefValueObjManager nameValue = (ICefValueObjManager)UdtManagerUtil.getUdtFactory().createManager("com.inspur.gs.bf.df.dfudt.udt.dfname.DFName");
      IDFName name = (IDFName)nameValue.createDataType();
      name.setDFName(orgName);
      AssociationInfo registDepartInfo = new AssociationInfo();
      registDepartInfo.setValue("RegistDepart", orgId);
      registDepartInfo.setValue("RegistDepart_CODE", code);
      registDepartInfo.setValue("RegistDepart_NAME", name);
      ValueObjModifyChangeDetail updateInfoChange = new ValueObjModifyChangeDetail();
      updateInfoChange.getPropertyChanges().put("CreatedBy", userName);
      updateInfoChange.getPropertyChanges().put("CreatedOn", new Date());
      DxcCommonData dxcCommonData = super.getData();

      for(int i = 0; i < dxcCommonData.getDataSet().size(); ++i) {
         dxcCommonData.pushDataByCode(i, "RegistDepart", registDepartInfo);
         dxcCommonData.pushDataByCode(i, "CreatInfo", updateInfoChange);
         dxcCommonData.pushDataByCode(i, "BIllStatus", "Billing");
      }

   }

   private String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }
}
