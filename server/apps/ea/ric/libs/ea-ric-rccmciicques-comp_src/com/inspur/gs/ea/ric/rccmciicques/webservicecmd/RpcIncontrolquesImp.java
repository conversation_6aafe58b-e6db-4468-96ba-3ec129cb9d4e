package com.inspur.gs.ea.ric.rccmciicques.webservicecmd;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class RpcIncontrolquesImp implements RpcIncontrolquesInter {
   private IBqlExecuter bqlExecuter;

   private IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   public void IncontrolquesFlag(String flag, String ID, String planID, String deptID) {
      if ("Pass".equals(flag)) {
         String PlanID = planID;
         String DeptID = deptID;
         this.deleteQues(ID);
         IBqlExecuter bqlExecuter = this.getBqlExecuter();
         ArrayList<String> refEntityIDs = new ArrayList();
         refEntityIDs.add("ec6d14c2-5773-48bd-99e4-5771be932c7d");
         IDbParameter[] Parameters = new IDbParameter[]{bqlExecuter.makeInParam("PlanID", planID), bqlExecuter.makeInParam("DeptID", deptID)};
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
         String bql = "SELECT ID FROM EAINCONTROLORGAN WHERE EAINCONTROLORGAN.PLANID = @PlanID AND EAINCONTROLORGAN.DEPTID = @DeptID";
         List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);
         IBqlExecuter bqlExecuter1 = this.getBqlExecuter();
         ArrayList<String> refEntityIDs1 = new ArrayList();
         IDbParameter[] Parameters1 = new IDbParameter[]{bqlExecuter1.makeInParam("CL", ID)};
         refEntityIDs1.add("569b828f-d70a-4c78-ba99-2e5ccc0d6d2d");
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
         String bql1 = "SELECT RISKCODE,RISKNAME,RISKGRADE,RISKDESCRIBE,CONTROLACTIVITYCODE,CONTROLACTIVITYDESC,CONTROLACTIVITYNAME,CONTROLMODE,CONTROLTARGETCODE,CONTROLTARGETNAME,EXECUFREQ,MANGERSYSTEM,PROCESSID,RESPDEPT,RESPPOSTION,PLANID,PARENTID,RESPDEPTID,RESPPOSTIONID FROM EAINCONTROLMATRIX WHERE EAINCONTROLMATRIX.PARENTID =  @CL";
         List<DynamicResultRow> resultCheckOrgType1 = bqlExecuter1.executeSelectStatement(bql1, refEntityIDs1, Parameters1);
         IBqlExecuter bqlExecuter2 = this.getBqlExecuter();
         ArrayList<String> refEntityIDs2 = new ArrayList();
         refEntityIDs2.add("569b828f-d70a-4c78-ba99-2e5ccc0d6d2d");
         IDbParameter[] Parameters2 = new IDbParameter[]{bqlExecuter2.makeInParam("CL", ID)};
         String bql2 = "SELECT QUESCODE,QUESTNAME,DEPTID,ID FROM EAINCONTROLQUES WHERE EAINCONTROLQUES.ID = @CL";
         List<DynamicResultRow> resultCheckOrgType2 = bqlExecuter.executeSelectStatement(bql2, refEntityIDs2, Parameters2);

         for(int i = 0; i < resultCheckOrgType1.size(); ++i) {
            IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("ZID", UUID.randomUUID().toString()), bqlExecuter.makeInParam("ParentId", ((DynamicResultRow)resultCheckOrgType.get(0)).getValues().get(0)), bqlExecuter.makeInParam("QuesCode", valueOf(((DynamicResultRow)resultCheckOrgType2.get(0)).getValues().get(0))), bqlExecuter.makeInParam("QuesName", valueOf(((DynamicResultRow)resultCheckOrgType2.get(0)).getValues().get(1))), bqlExecuter.makeInParam("DeptId", valueOf(((DynamicResultRow)resultCheckOrgType2.get(0)).getValues().get(2))), bqlExecuter.makeInParam("RiskCode", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(0))), bqlExecuter.makeInParam("RiskName", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(1))), bqlExecuter.makeInParam("RiskGrade", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(2))), bqlExecuter.makeInParam("RISKDESCRIBE", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(3))), bqlExecuter.makeInParam("ControlActivityCode", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(4))), bqlExecuter.makeInParam("ControlActivityDesc", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(5))), bqlExecuter.makeInParam("ControlActivityName", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(6))), bqlExecuter.makeInParam("ControlMode", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(7))), bqlExecuter.makeInParam("ControlTargetCode", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(8))), bqlExecuter.makeInParam("ControlTargetName", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(9))), bqlExecuter.makeInParam("ExecuFreq", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(10))), bqlExecuter.makeInParam("MangerSystem", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(11))), bqlExecuter.makeInParam("ProcessId", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(12))), bqlExecuter.makeInParam("RespDept", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(13))), bqlExecuter.makeInParam("RespPostion", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(14))), bqlExecuter.makeInParam("PlanId", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(15))), bqlExecuter.makeInParam("QuesId", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(16))), bqlExecuter.makeInParam("RespDeptId", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(17))), bqlExecuter.makeInParam("RespPostionId", valueOf(((DynamicResultRow)resultCheckOrgType1.get(i)).getValues().get(18)))};
            String InsertBql = "INSERT INTO EAINCONTROLQUESDETAILS(ID,PARENTID,PLANID,QUESID,RISKCODE,RISKNAME,RISKGRADE,RESPDESCRIBE,CONTROLACTIVITYCODE,CONTROLACTIVITYDESC,CONTROLACTIVITYNAME,CONTROLMODE,CONTROLTARGETCODE,CONTROLTARGETNAME,EXECUFREQ,MANGERSYSTEM,PROCESSID,RESPDEPT,RESPPOSTION, QUESCODE,QUESTNAME,DEPTID,RESPDEPTID,RESPPOSTIONID) values(:ZID, :ParentId,:PlanId,:QuesId,:RiskCode, :RiskName, :RiskGrade, :RISKDESCRIBE, :ControlActivityCode, :ControlActivityDesc, :ControlActivityName, :ControlMode, :ControlTargetCode, :ControlTargetName, :ExecuFreq, :MangerSystem, :ProcessId, :RespDept, :RespPostion, :QuesCode, :QuesName, :DeptId, :RespDeptId, :RespPostionId)";
            bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            ArrayList<String> refEntityIDs3 = new ArrayList();
            refEntityIDs2.add("ec6d14c2-5773-48bd-99e4-5771be932c7d");
            bqlExecuter.executeBqlStatement(InsertBql, refEntityIDs3, iDbParameters);
         }

         for(int i = 0; i < resultCheckOrgType2.size(); ++i) {
            IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("QuesId", ((DynamicResultRow)resultCheckOrgType2.get(i)).getValues().get(3)), bqlExecuter.makeInParam("DeptID", DeptID), bqlExecuter.makeInParam("PlanID", PlanID)};
            String InsertBql3 = "UPDATE EAINCONTROLORGAN set QUESID = :QuesId where EAINCONTROLORGAN.DEPTID = :DeptID AND EAINCONTROLORGAN.PLANID = :PlanID";
            bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            ArrayList<String> refEntityIDs3 = new ArrayList();
            refEntityIDs3.add("4b210063-b3a5-4371-b351-c5ef1ae03d50");
            bqlExecuter.executeBqlStatement(InsertBql3, refEntityIDs3, iDbParameters);
         }
      }

      this.AlterState1VOAction(ID);
      this.UpdateInControlMatrixVOAction(ID);
      this.UpdateInControlOrgan1VOAction(ID);
   }

   public static String valueOf(Object obj) {
      return obj == null ? "" : obj.toString();
   }

   public void deleteQues(String ID) {
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] queryParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("quesId", ID)};
      String sqlselect = "select Id from EAINCONTROLORGAN where quesId =:quesId";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> dept = this.bqlExecuter.executeSelectStatement(sqlselect, planIDs, queryParams);
      String parentId = (String)((DynamicResultRow)dept.get(0)).get("Id");
      IDbParameter[] deleteParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("parentId", parentId)};
      String sqldelete = "delete from  EAINCONTROLQUESDETAILS where parentId =:parentId";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      this.bqlExecuter.executeBqlStatement(sqldelete, planIDs, deleteParams);
   }

   public void AlterState1VOAction(String ID) {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("569b828f-d70a-4c78-ba99-2e5ccc0d6d2d");
      IDbParameter[] iDbParameters = new IDbParameter[1];
      iDbParameters[0] = bqlExecuter.makeInParam("CL", ID);
      String bql = "update InControlQues.InControlQues  set Bstatus = '1' where ID = @CL";
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
      bqlExecuter.executeBqlStatement(bql, refEntityIDs, iDbParameters);
   }

   public void UpdateInControlMatrixVOAction(String ID) {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("569b828f-d70a-4c78-ba99-2e5ccc0d6d2d");
      IDbParameter[] Parameters = new IDbParameter[]{bqlExecuter.makeInParam("CL", ID)};
      String bql = "SELECT QUESCODE,QUESTNAME,DEPTID,PLANID FROM EAINCONTROLQUES WHERE EAINCONTROLQUES.ID  = @CL";
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);

      for(int i = 0; i < resultCheckOrgType.size(); ++i) {
         IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("QuesCode", valueOf(((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(0))), bqlExecuter.makeInParam("QuesName", valueOf(((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(1))), bqlExecuter.makeInParam("DeptId", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(2)), bqlExecuter.makeInParam("PlanId", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(3)), bqlExecuter.makeInParam("CL", ID)};
         String InsertBql = "UPDATE EAINCONTROLMATRIX set QUESCODE = :QuesCode, QUESTNAME = :QuesName, DEPTID = :DeptId, PLANID = :PlanId where EAINCONTROLMATRIX.PARENTID = :CL";
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs1 = new ArrayList();
         refEntityIDs1.add("4b210063-b3a5-4371-b351-c5ef1ae03d50");
         bqlExecuter.executeBqlStatement(InsertBql, refEntityIDs1, iDbParameters);
      }

   }

   public void UpdateInControlOrgan1VOAction(String ID) {
      IBqlExecuter bqlExecuter = this.getBqlExecuter();
      ArrayList<String> refEntityIDs = new ArrayList();
      refEntityIDs.add("569b828f-d70a-4c78-ba99-2e5ccc0d6d2d");
      IDbParameter[] Parameters = new IDbParameter[]{bqlExecuter.makeInParam("CL", ID)};
      String bql = "SELECT ID FROM EAINCONTROLQUES WHERE EAINCONTROLQUES.ID  = @CL";
      bqlExecuter.getOptions().setOptionType(OptionType.ExecuteBql);
      List<DynamicResultRow> resultCheckOrgType = bqlExecuter.executeSelectStatement(bql, refEntityIDs, Parameters);

      for(int i = 0; i < resultCheckOrgType.size(); ++i) {
         IDbParameter[] iDbParameters = new IDbParameter[]{bqlExecuter.makeInParam("QuesId", ((DynamicResultRow)resultCheckOrgType.get(i)).getValues().get(0))};
         String InsertBql = "UPDATE EAINCONTROLORGAN set BSTATUS = '1' where EAINCONTROLORGAN.QUESID = :QuesId";
         bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         ArrayList<String> refEntityIDs1 = new ArrayList();
         refEntityIDs1.add("4b210063-b3a5-4371-b351-c5ef1ae03d50");
         bqlExecuter.executeBqlStatement(InsertBql, refEntityIDs1, iDbParameters);
      }

   }
}
