package com.inspur.gs.ea.ric.rccmciicques.webservicecmd;

import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

@GspServiceBundle(
   applicationName = "ea",
   serviceUnitName = "ric",
   serviceName = "Incontrolques_service"
)
public interface RpcIncontrolquesInter {
   @RpcServiceMethod(
      serviceId = "com.inspur.gs.ea.ric.rccmciicques.webservicecmd.RpcIncontrolques.IncontrolquesFlag"
   )
   void IncontrolquesFlag(@RpcParam(paramName = "flag") String var1, @RpcParam(paramName = "ID") String var2, @RpcParam(paramName = "planID") String var3, @RpcParam(paramName = "deptID") String var4);
}
