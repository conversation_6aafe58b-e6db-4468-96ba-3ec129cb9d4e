package com.inspur.gs.ea.ric.ricclce.complievent.common;

import com.inspur.edp.cef.entity.entity.AssoInfoBase;
import com.inspur.edp.cef.entity.entity.EntityDataUtils;
import com.inspur.edp.cef.entity.entity.IEntityData;
import com.inspur.edp.cef.entity.entity.IEntityDataCollection;
import com.inspur.edp.common.commonudt.administrativeinfo.entity.IAdministrativeInfo;
import com.inspur.edp.common.commonudt.billstate.entity.IBillState;
import com.inspur.edp.common.commonudt.processinstance.entity.IProcessInstance;
import java.util.Date;

public final class CompliEventUtils {
   public static String getID(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "ID");
   }

   public static void setID(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "ID", propertyValue);
   }

   public static Date getVersion(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "Version");
   }

   public static void setVersion(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "Version", propertyValue);
   }

   public static String getCode(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Code");
   }

   public static void setCode(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Code", propertyValue);
   }

   public static String getName(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Name");
   }

   public static void setName(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Name", propertyValue);
   }

   public static String getReason(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Reason");
   }

   public static void setReason(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Reason", propertyValue);
   }

   public static String getDescription(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Description");
   }

   public static void setDescription(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Description", propertyValue);
   }

   public static Date getOccurDate(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "OccurDate");
   }

   public static void setOccurDate(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "OccurDate", propertyValue);
   }

   public static AssoInfoBase getOccurComp(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "OccurComp");
   }

   public static void setOccurComp(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "OccurComp", propertyValue);
   }

   public static AssoInfoBase getRegistDepart(IEntityData data) {
      return (AssoInfoBase)EntityDataUtils.getValue(data, "RegistDepart");
   }

   public static void setRegistDepart(IEntityData data, AssoInfoBase propertyValue) {
      EntityDataUtils.setValue(data, "RegistDepart", propertyValue);
   }

   public static String getRegistrant(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "Registrant");
   }

   public static void setRegistrant(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "Registrant", propertyValue);
   }

   public static String getEventStatus(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EventStatus");
   }

   public static void setEventStatus(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EventStatus", propertyValue);
   }

   public static IAdministrativeInfo getCreateInfo(IEntityData data) {
      return (IAdministrativeInfo)EntityDataUtils.getValue(data, "CreateInfo");
   }

   public static void setCreateInfo(IEntityData data, IAdministrativeInfo propertyValue) {
      EntityDataUtils.setValue(data, "CreateInfo", propertyValue);
   }

   public static IBillState getBIllStatus(IEntityData data) {
      return (IBillState)EntityDataUtils.getValue(data, "BIllStatus");
   }

   public static void setBIllStatus(IEntityData data, IBillState propertyValue) {
      EntityDataUtils.setValue(data, "BIllStatus", propertyValue);
   }

   public static IProcessInstance getIstanceID(IEntityData data) {
      return (IProcessInstance)EntityDataUtils.getValue(data, "IstanceID");
   }

   public static void setIstanceID(IEntityData data, IProcessInstance propertyValue) {
      EntityDataUtils.setValue(data, "IstanceID", propertyValue);
   }

   public static Date getCreateDate1(IEntityData data) {
      return (Date)EntityDataUtils.getValue(data, "CreateDate1");
   }

   public static void setCreateDate1(IEntityData data, Date propertyValue) {
      EntityDataUtils.setValue(data, "CreateDate1", propertyValue);
   }

   public static String getEXT1(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT1");
   }

   public static void setEXT1(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT1", propertyValue);
   }

   public static String getEXT2(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT2");
   }

   public static void setEXT2(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT2", propertyValue);
   }

   public static String getEXT3(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT3");
   }

   public static void setEXT3(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT3", propertyValue);
   }

   public static String getEXT4(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT4");
   }

   public static void setEXT4(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT4", propertyValue);
   }

   public static String getEXT5(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT5");
   }

   public static void setEXT5(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT5", propertyValue);
   }

   public static String getEXT6(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT6");
   }

   public static void setEXT6(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT6", propertyValue);
   }

   public static String getEXT7(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT7");
   }

   public static void setEXT7(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT7", propertyValue);
   }

   public static String getEXT8(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT8");
   }

   public static void setEXT8(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT8", propertyValue);
   }

   public static String getEXT9(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT9");
   }

   public static void setEXT9(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT9", propertyValue);
   }

   public static String getEXT10(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT10");
   }

   public static void setEXT10(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT10", propertyValue);
   }

   public static String getEXT11(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT11");
   }

   public static void setEXT11(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT11", propertyValue);
   }

   public static String getEXT12(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT12");
   }

   public static void setEXT12(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT12", propertyValue);
   }

   public static String getEXT13(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT13");
   }

   public static void setEXT13(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT13", propertyValue);
   }

   public static String getEXT14(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT14");
   }

   public static void setEXT14(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT14", propertyValue);
   }

   public static String getEXT15(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT15");
   }

   public static void setEXT15(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT15", propertyValue);
   }

   public static String getEXT16(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT16");
   }

   public static void setEXT16(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT16", propertyValue);
   }

   public static String getEXT17(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT17");
   }

   public static void setEXT17(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT17", propertyValue);
   }

   public static String getEXT18(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT18");
   }

   public static void setEXT18(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT18", propertyValue);
   }

   public static String getEXT19(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT19");
   }

   public static void setEXT19(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT19", propertyValue);
   }

   public static String getEXT20(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT20");
   }

   public static void setEXT20(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT20", propertyValue);
   }

   public static String getEXT21(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT21");
   }

   public static void setEXT21(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT21", propertyValue);
   }

   public static String getEXT22(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT22");
   }

   public static void setEXT22(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT22", propertyValue);
   }

   public static String getEXT23(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT23");
   }

   public static void setEXT23(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT23", propertyValue);
   }

   public static String getEXT24(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT24");
   }

   public static void setEXT24(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT24", propertyValue);
   }

   public static String getEXT25(IEntityData data) {
      return (String)EntityDataUtils.getValue(data, "EXT25");
   }

   public static void setEXT25(IEntityData data, String propertyValue) {
      EntityDataUtils.setValue(data, "EXT25", propertyValue);
   }

   public static IEntityDataCollection getCompliEventFiles(IEntityData data) {
      return EntityDataUtils.getChildDatas(data, "CompliEventFile");
   }
}
