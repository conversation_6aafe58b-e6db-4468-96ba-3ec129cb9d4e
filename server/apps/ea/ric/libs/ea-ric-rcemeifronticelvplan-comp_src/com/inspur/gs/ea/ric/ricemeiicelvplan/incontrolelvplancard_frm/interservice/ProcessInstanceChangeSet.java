package com.inspur.gs.ea.ric.ricemeiicelvplan.incontrolelvplancard_frm.interservice;

import com.fasterxml.jackson.core.type.TypeReference;
import com.inspur.edp.internalservice.api.changeset.AbstractUdtChangeSet;
import com.inspur.edp.internalservice.api.changeset.OperateType;

public class ProcessInstanceChangeSet extends AbstractUdtChangeSet {
   private String processInstance;

   public ProcessInstanceChangeSet(OperateType operateType) {
      super(operateType);
   }

   public String getProcessInstance() {
      return (String)this.getPropValue("processInstance", new TypeReference<String>() {
      });
   }

   public void setProcessInstance(String processInstance) {
      this.setPropValue("processInstance", processInstance);
   }
}
