package com.inspur.gs.ea.ric.rccscr.processcheckcard_frm.voactions;

import com.inspur.edp.bff.spi.AbstractFSAction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.permission.api.manager.runtime.PermissionManager;

public class GetOperationVOAction extends AbstractFSAction<Boolean> {
   public void execute() {
      PermissionManager permissionManager = (PermissionManager)SpringBeanUtils.getBean(PermissionManager.class);
      Boolean flag = permissionManager.isPrincipalHasOp("54643eb2-027c-1c7d-9876-a26f101c27a2");
      this.setResult(flag);
   }
}
