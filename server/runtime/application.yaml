bef:
  session-state:
    mode: InProc
qdp:
  license:
    perpetualspreadjs: NVNTVlZZWWxrUVJoZDRVSFpoTm5RNUx5MzBRNVJZUldwQk9WUjBTRTNSWTY1aFdreHZRbGh5YmxrNldFRm1TbHBwZFZVNFZWZDRiNHhvTW5seEw1ZHFRVXhuY0V0clNUY3hjV3BEYmpsdU1VeFJVV2hoVUVad1p6ZFBOa0p1VEdNNU9FSldkWGRKYW1KclY1bFFZelJPWno0M0lpd2lSQ0kwZXlKQmJtd2lPbnNpWkhOeUlqcG1ZV3h6WlN3aVpteG5JanBiWFg0c0lrbGtJam9pTXprek9EazRNek03TWpZeE16RTlJaXdpUTQ5aElqb2k5cldxOXI2dTBZQ2E5OVNvMEw2djlMdTY5cHlKMFptUTlZV3M5WSUyQjhJaXdpUTdKNElqb2lNakF5TXpBeE1EVWdNRGd5TWpBd0lpd2lVSEprSWpwYmV5Sk9Jam9pVTdCeVpXRmtJRXBUSUhZdU1USWlMQ0pESWpvaVFqbEdTU0ozWFg1M2V5SmZjaUkwTVRNOE56QXdNelU1T0N3aVNDSTBJakl6TWtFelE0TXpJaXdpVXlJMElsb3pValV2ZWpCSWJqbFlURmt4U0d0RVRrZFNiNnRqZEhoR1VWQkRPRmxaU2tKSE1Ea3ZRVTR3UWtsR1Q3TTdZVk5UZURkSVN6RklSemhTTDdseVpqRnBRNjVoU1RGaU5ETXJVbEV4ZURjOFZXNU9ZNk5HWVhGN1pFVnJhRWhxTjR0YVU2eG9UekF6T0hJdlJ6ZzhXa050V0Vkd1NGTk5iRWQwUlRSTlE3a3lZVTk5WjZ0bEs2aGlibWM1VldRdmJuVnViNVF4UnpCeGNFRkdWelJRZFN0UGFYVnVhazNZZFhoeU40eGpjblU0VDRaV1RWRjVNSGM5UzZKWVlUWm9jNmRqVEZGUFZ5MnhOVU5uY1d4bmVHUmlONjVZTUdGUGNGQkplVkV4WlhCcFQ1aHdRa2xZWVZWU1M1MEkwQiUyMzkxMzE2MjczMzQ5ODM5M3lwQ19nbmFMQnpSeFZuYnZRVw==
    perpetualspreadjsV15: NVNTVlZZWWxrUVlaa3hVRk54VkZGTVNXRjBTNWhSWmpNOFpUSjZVNVptYlV3dk16RjRla1U4UjZwbFVrcGthSFpYVFRGamFVZDhUazNIWkhOa1ZqZEdlbmhyVm1KUmVqQlJjRFZZVFZkTVM2OVdhbGxDYWxsNVVqaDhiNmxXUkN0bk42ODVTa3hGVkVSU05UbE1WWFY3UFQ0aUxDSkVJanAxSWtGdWJDSTBleUprYzdJaU9tWmhiSE5sTENKbWJHY2lPbHRkZlN3aVNXUWlPaUl5TkRjOU56VXpNalE2TlRjOE1USWlMQ0pEVG1FaU9pTG10YXJtdmExcGdKcm5sS2pvdmElMkZrdTFibW5JbnBtWkRsaGF6bGoxZ2lMQ0pEY25RaU9pSXlNREl6TURJd015QXdPVFU2TXpBaUxDSlFjbVFpT2x0MUlrOGlPaUpUY0hKbFlXUWdTbE1nZGk4eE5TSXNJa01pT2lKVFVVRk1JbjVkZlg0JTIzZXlKZmNpSTBNakF5TnprN05qQTVOQ3dpU0NJMElrWkVPVGhCTTRJaUxDSlRJam9pWXpCTVpHRkhTbVp5TDZsTVFXM1ZWNUowVVdSNlJFVnlUV1l3VDdVdlNUTnpiWFZPTHpKbWJ5M3liNmxOY21zeU9XOHZNa2t3UnpSSlJGSjZWbVpPYUczaGQ2bGxUVkl5WVdSdVU3RlBkNVk5T1N0UU03SktlVEYwWmpROWJFdG5kNHQ0WVVoWGJEZ3ljRXBFUkdWdFU0cFlVek5PVkRBNUs1WjVUR0k1UkRGTlRFWXhUSEl5VGtweFJYaFJSVTU1U2xwOGJFVm5TNkY3UjZSV01UbHRPRzloVURsWmE1WlJNWEU0YlV4NmFuSlFRblJuUm5vclVuaDljbmt6VW05Q2FUaHpTbk15T1U5UU5saDVhREZRZFZaSk5VcGxZbXRsY1daaU1rOVFNVk15ZDdOb1F6TlBRa1Z6TU1sZjBCJTIzMjE4NzU2NDIzNTc5NzQyeXBDX2duYWlsQnpSeFZuYnZRVw==
docs-viewer:
  limits:
    pdf:
      size: 50m
    word:
      size: 50m
    excel:
      size: 20m
      row: 10000
      column: 1000
    ppt:
      size: 20m
  layout:
    download: false
    header: false
print:
  changeDataLanguage: false
  port: 8281
  showCloseButton: false
  showTag: false
  fontName: SimSun
event-configurations:
  eventManagers:
  - name: NoCodeServiceEventManager
    listeners:
    - name: NocodeDesignSchemaChangeListener
      implClassName: com.inspur.edp.web.designschema.synchronization.NocodeDesignSchemaChangeListener
  - name: MetadataRtEventManager
    listeners:
    - name: VoRtEventListener
      implClassName: com.inspur.edp.customize.impl.VoRtEventListener
    - name: BeRtEventListener
      implClassName: com.inspur.edp.customize.impl.BeRtEventListener
    - name: BeCoincidenceEventListener
      implClassName: com.inspur.edp.bef.bemanager.metadatartevent.BeMetadataRtEventListener
    - name: BffMetadataRtEventListener
      implClassName: com.inspur.edp.formserver.vmmanager.metadataeventlistener.BffMetadataRtEventListener
  - name: MdpkgChangedEventManager
    listeners:
    - name: SgfMdPkgChangedEventListener
      implClassName: com.inspur.edp.sgf.runtime.customize.core.listener.SgfMdPkgChangedEventListener
    - name: QOMetadataChangedEventListener
      implClassName: com.inspur.edp.qdp.lcp.interpretation.eventlistener.QOMetadataChangedEventListener
  - name: MetadataCreateEventManager
    listeners:
    - name: MetadataCreateEventListener
      implClassName: com.inspur.edp.lcm.metadata.core.event.MetadataCreateEventListenerImpl
    - name: FrontMetadataCreateListener
      implClassName: com.inspur.edp.web.pageflow.metadata.listener.PageFlowMetadataEventListener
  - name: MetadataEventManager
    listeners:
    - name: MetadataEventListener
      implClassName: com.inspur.edp.lcm.metadata.core.event.MetadataEventListenerImpl
    - name: WebCodeListener
      implClassName: com.inspur.edp.web.sourcecode.metadata.listener.SourceCodeMetadataEventListener
    - name: PageFlowListener
      implClassName: com.inspur.edp.web.pageflow.metadata.listener.PageFlowMetadataEventListener
    - name: DesignSchemaChangeListener
      implClassName: com.inspur.edp.web.designschema.synchronization.DesignSchemaChangeListener
    - name: FormMetadataSaveEventListener
      implClassName: com.inspur.edp.web.formmetadata.event.FormMetadataSaveEventListener
    - name: BeMetadataEventListener
      implClassName: com.inspur.edp.bef.bemanager.BeMetadataEventListener
    - name: BffMetadataEventListener
      implClassName: com.inspur.edp.formserver.vmmanager.BffMetadataEventListener
  - name: DirEventManager
    listeners:
    - name: DirEventListener
      implClassName: com.inspur.edp.lcm.metadata.core.event.DirEventListenerImpl
  - name: WorkItemEventManager
    listeners:
    - name: IWorkItemEventListener
      implClassName: com.inspur.edp.wf.engine.event.listener.TaskCenterListener
    - name: WorkItemMessageNoticeListener
      implClassName: com.inspur.edp.wf.engine.event.listener.MessageNoticeListener
    - name: WorkItemToolHandlingListener
      implClassName: com.inspur.edp.wf.engine.event.listener.ToolHandlingListener
  - name: ActivityInstanceEventManager
    listeners:
    - name: IActivityInstanceEventListener
      implClassName: com.inspur.edp.wf.engine.event.listener.ToolHandlingListener
    - name: ActivityMessageNoticeListener
      implClassName: com.inspur.edp.wf.engine.event.listener.MessageNoticeListener
  - name: ProcessInstanceEventManager
    listeners:
    - name: IProcessInstanceEventListener
      implClassName: com.inspur.edp.wf.engine.event.listener.ToolHandlingListener
    - name: ProcessMessageNoticeListener
      implClassName: com.inspur.edp.wf.engine.event.listener.MessageNoticeListener
    - name: TaskLogListenerForPF
      implClassName: com.inspur.edp.wf.extend.listener.TaskLogListenerForPF
  - name: ProcessDefinitonEventManager
    listeners:
    - name: IProcessDefinitionEventListener
      implClassName: com.inspur.edp.wf.engine.event.listener.ProcessDefinitonListener
  - name: BefDtConsistencyCheckEventManager
    listeners:
    - name: BizEntityDTConsistencyCheckListener
      implClassName: com.inspur.edp.bef.bizentitydtconsistencychecklistener.BizEntityDTConsistencyCheckListener
    - name: BizEntityFieldDTConsistencyCheckListener
      implClassName: com.inspur.edp.bef.bizentitydtconsistencychecklistener.BizEntityFieldDTConsistencyCheckListener
    - name: BizEntityActionDTConsistencyCheckListener
      implClassName: com.inspur.edp.formserver.vmmanager.vodtconsistencychecklistener.BizEntityActionDTConsistencyCheckListener
    - name: VoDTConsistencyCheckListener
      implClassName: com.inspur.edp.formserver.vmmanager.vodtconsistencychecklistener.VoDTConsistencyCheckListener
    - name: VoFieldDTConsistencyCheckListener
      implClassName: com.inspur.edp.formserver.vmmanager.vodtconsistencychecklistener.VoFieldDTConsistencyCheckListener
    - name: UdtDtConsistencyCheckListener
      implClassName: com.inspur.edp.udt.designtime.manager.udtdtconsistencychecklistener.UdtDtConsistencyCheckListener
    - name: UdtFieldDtConsistencyCheckListener
      implClassName: com.inspur.edp.udt.designtime.manager.udtdtconsistencychecklistener.UdtFieldDtConsistencyCheckListener
  - name: PushChangeSetEventManager
    listeners:
    - name: PushChangeSet
      implClassName: com.inspur.edp.formserver.vmmanager.PushChangeSetListener
  - name: BefSaveEventManager
    listeners:
    - name: bpm
      implClassName: com.inspur.edp.bpm.runtime.impl.BefSaveEventListener
    - name: Rri
      implClassName: com.inspur.edp.rri.publisher.service.BaseOnBEListener
    - name: Aif
      implClassName: com.inspur.edp.aif.runtime.client.impl.BefSaveEventListener
    - name: BefAuditLog
      implClassName: com.inspur.edp.common.component.auditlog.BefAuditLogEventListener
    - name: Chgdr
      implClassName: com.inspur.edp.chgdr.adapter.bef.ChgdrBefSaveEventListener
  - name: StreamSubscriptionEventManager
    listeners:
    - name: Rri
      implClassName: com.inspur.edp.rri.subscriber.eventlistener.SuFilterListener
  - name: FilterInvokeEventManager
    listeners:
    - name: BqlExecuterFilter
      implClassName: com.inspur.edp.qdp.bql.execute.listener.BqlExecuterCacheFilterListener
    - name: FrameworkFilter
      implClassName: io.iec.edp.caf.runtime.core.manager.FrameworkFilterListener
  - name: FrameworkEventManager
    listeners:
    - name: frameworkEventListener
      implClassName: com.inspur.edp.bef.core.session.ResponseSession
paas:
  enabled: false
rtf-scheduler-configurations:
  scheduler:
    persistenceEnable: false
    clustered: false
    distributionMaster: false
    tenants: null
  dataSource:
    driver: null
    url: null
    user: null
    password: null
caf-security:
  authn:
    flows:
      api-key:
        enabled: true
        signature:
          enabled: true
      form-login:
        enabled: true
        login-page-uri: /login.html
        login-processing-uri: /sign-in
        login-success-uri: /platform/runtime/sys/web/index.html#/ssologin
        login-failure-uri: /login.html#error=
      passport:
        enabled: true
        authorization-uri: https://id.inspuronline.com/oauth2.0/authorize
        redirect-uri: /oauth2.0/sign-in
        accessToken-uri: https://id.inspuronline.com/oauth2.0/token
        user-info-uri: https://id.inspuronline.com/oauth2.0/user-info
        login-success-uri: /api/runtime/sys/v1.0/routerRedirect
        login-failure-uri: /login-info.html#error=
        client-id: xxxx
        client-secret: xxxx
      http-basic:
        enabled: true
    providers:
      jpa-api-key:
        enabled: true
      username-password-jdbc:
        enabled: true
        datasource:
          type: com.zaxxer.hikari.HikariDataSource
          url: ENC(gXYqk50ecni3KhjvZL/fsSTY8cpWTP65hsyCpWnp66/B6FMYPEYk8Q+Qb6ZZvveycBkhTFocKCh7tqdUSJ0GNw==)
          driver-class-name: org.postgresql.Driver
          username: ENC(qOXUneSZxSstMRvGghH1/A4z6yNwSf3He0VscnQfhwc=)
          password: ENC(a1XaQJkZvTdOFXJ8BhgNXMPuWVMP5wv+kV6LHDbtr7ZANY4lfBft1w==)
        secret-column: password
        secret-key-column: userid
        secret-key-algorithm: DES
        attributes-mapping:
          id: userid
          username: usercode
          fullName: username
          accountState: islock
          external-attributes:
            SysOrgId: sysorgid
            SysOrgCode: sysorgcode
            SysOrgName: sysorgname
        password-strength-rules:
        - password-rule: (?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,}
          description: 密码中必须包含大小写字母、数字、特殊符号，至少8个字符
    rate-limit:
      enabled: true
      duration-in-minute: 10
      limit: 5
      apikey:
        enabled: true
        duration-in-minute: 5
        limit: 100
    rules:
    - matchers:
      - /actuator/**
      flows:
      - API_KEY
    - matchers:
      - /api/runtime/sys/v1.0/routerRedirect
      flows:
      - PASSPORT
    - matchers:
      - /api/runtime/sys/v1.0/liccache/clear/**
      flows:
      - API_KEY
    - matchers:
      - /api/ba/bair/v1.0/login
      flows:
      - HTTP_BASIC
    - matchers:
      - /api/erm/ermfm/v1.0/borrowout/login
      flows:
      - API_KEY
    - matchers:
      - /api/mdm/mdc/DataReceiveService/v1.0/mdc-api/*
      flows:
      - API_KEY
    - matchers:
      - /api/mdm/mdc/AccessToDictionaryDataService/v1.0/mdc-api/*
      flows:
      - API_KEY
    - matchers:
      - /api/mdm/mda/DataPushService/v1.0/mda-datapush/*
      flows:
      - API_KEY
    - matchers:
      - /api/mdm/mda/DataPublishService/v1.0/mda-api/*
      flows:
      - API_KEY
    - matchers:
      - /api/mdm/*
      flows:
      - API_KEY
    - matchers:
      - /api/**
      flows:
      - FORM_LOGIN
  general:
    white-list:
    - /api/runtime/sys/v1.0/liccache/clear/*
    - /
    - /actuator/info
    - /actuator/health
    - /**/*.js
    - /**/*.css
    - /**/*.html
    - /**/*.png
    - /**/*.jpg
    - /**/*.svg
    - /**/*.ttf
    - /platform/runtime/sys/web/login_module/i18n/*.properties
    - /api/runtime/sys/v1.0/authenstrategies/*/*
    - /api/runtime/sys/v1.0/loginInfo
    - /api/runtime/sys/v1.0/oauth2configs
    - /api/runtime/sys/v1.0/authenitems/reset
    - /api/runtime/sys/v1.0/logout
    - /api/runtime/sys/v1.0/rtf-configuration/loginresource/*/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.msu.api.server.MsuServiceRegistry.register/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.msu.api.server.MsuServiceRegistry.deRegister/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.msu.api.server.MsuServiceDiscovery.discover/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.rpc.api.service.RpcDefinitionQuery.queryList/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.rpc.api.service.RpcDefinitionQuery.detail/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.rpc.api.service.RpcDefinitionRegistry.register/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.rpc.registry.api.RpcDefinitionQuery.queryList/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.rpc.registry.api.RpcDefinitionQuery.detail/*
    - /api/runtime/communication/v1.0/rpc/io.iec.edp.caf.rpc.registry.api.RpcDefinitionRegistry.register/*
    - /api/runtime/sys/v1.0/authenitems/before
    - /api/runtime/sys/v1.0/certificates/*
    - /api/runtime/sys/v1.0/myauthenconfigs
    - /api/runtime/sys/v1.0/authenitems/vCode
    - /**/*.apk
    - /api/runtime/sys/v1.0/authenitems/rule/passwordstrategy/*
    - /api/runtime/sys/v1.0/ldap/state/*/*
    - /api/runtime/sys/v1.0/loginInfos
    - /api/runtime/sys/v1.0/rtf-configuration/i18nresource/*/*
    - /api/runtime/sys/v1.0/sms/smscode/*/*
    - /api/runtime/sys/v1.0/fp/**
    - /api/runtime/sys/v1.0/rtf-contact-channel
    - /api/runtime/sys/v1.0/sms/smscode
    - /platform/runtime/sys/web/rtf.common.manifest.json
    - /platform/runtime/common/web/runtime.common.manifest.json
    logout-uri: /logout
    logout-success-uri: /login.html
caf-boot:
  server:
    static-resources:
      external-static-resources:
      - file:///$EDP_HOME/../components/
      - file:///$EDP_HOME/../web/
      enable-cache: true
    home-page: /index.html
    mime-mapping:
    - extension: ngfactory
      mime-type: text/javascript
  bsession:
    timeout: 2592000
  websession:
    type: Default
    apikeyPersistence: false
  persistence:
    gsp-cloud:
      compatibility-mode: true
  serialization:
    json:
      max-string-length: 100M
      max-nesting-depth: 1K
      max-num-length: 1K
      max-name-length: 50K
      max-doc-length: -1L
rtf-configurations:
  framework:
    dashboardDisplay: true
  login:
    ssoRedirectUri: /platform/runtime/sys/web/index.html#/ssologin
    ssoEnType: RSA
    absolutePath: false
    dualStack: false
    nStackHost: null
    logoutServiceUri: /logout.html
  func-query-extension:
    name: rtcMenuExtension
expression-settings:
  functionCategories:
  - functionList:
    - name: getEncryptedUriByMap
      semanticName: 单点登录整体加密
      instanceName: getEncryptedUriByMap
      className: io.iec.edp.caf.runtime.sso.core.manager.SsoEncryptManagerImpl
      description: 单点登录整体加密
      funParameterList:
      - name: param
        description: String类型的Query参数
    - name: getEncodeParam
      semanticName: Param参数加密
      instanceName: getEncodeParam
      className: io.iec.edp.caf.runtime.sso.core.manager.SsoEncryptManagerImpl
      description: Entityparam或Queryparam加密
      funParameterList:
      - name: param
        description: JSON类型的Entityparam或Queryparam参数
  defaultEngineName: js
  engineItems:
  - name: js
  functions:
  - token: getEmployeeByUserID
    su: df
    serviceId: com.inspur.gs.bf.df.commonservice.core.data.BFExpressions.getEmployeeByUserID
    name: 根据用户ID获取行政人员信息。
    description: |-
      根据用户ID获取行政人员信息。返回的对象有以下属性：
      ID：行政人员的ID。
      OrganizationID：行政人员所在组织的ID。

      以下是取行政人员ID的示例：
      内置扩展调用("getEmployeeByUserID",用户ID).get("ID")
    params:
    - paramName: userID
      paramDesc: 用户ID
  - token: getAdminCompanyByUserID
    su: df
    serviceId: com.inspur.gs.bf.df.commonservice.core.data.BFExpressions.getAdminCompanyByUserID
    name: 根据用户ID获取所属行政单位ID
    description: 根据用户ID获取所属行政单位ID
    params:
    - paramName: userID
      paramDesc: 用户ID
  - token: getContext
    su: df
    serviceId: com.inspur.gs.bf.df.commonservice.core.data.BFExpressions.getContext
    name: 获取上下文属性
    description: 获取上下文属性
    params:
    - paramName: key
      paramDesc: 要获取的属性的key
  - token: getExFieldValue
    su: df
    serviceId: com.inspur.gs.bf.df.commonservice.core.data.BFExpressions.getExFieldValue
    name: 获取扩展字段值
    description: "使用示例：比如在物料类型上添加了自定义字段 EXTC_STR1 ，希望将其内容追加在流程标题中，则可以在流程标题的表达式中拼接：\
      \n内置扩展调用(\"getExFieldValue\", \"bfmaterialtype\", \"exec_str1\", \"id='\"+获取\
      上下文变量(\"单据内码\")+\"'\") "
    params:
    - paramName: tableName
      paramDesc: 表名
    - paramName: fieldName
      paramDesc: 字段名
    - paramName: filter
      paramDesc: 过滤条件
rtf-authen:
  login:
    vcode: false
    algorithm: DES
    securityRule: true
    successUri: /platform/runtime/sys/web/index.html#/ssologin
    failureUri: /login.html?error=
rtf-system-integration:
  emm:
    enable: false
    emmHost: xxxx
    serverId: xxxx
    serverSecret: xxxx
caf-rpc:
  localInvoke:
    corePoolSize: 32
    maxPoolsize: 200
  v1:
    enableClientCache: false
caf-lock:
  enableAlone: false
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
management:
  endpoints:
    web:
      exposure:
        include: null
csp:
  collector:
    enabled: false
    client-base-url: null
    client-health-check-uri: /actuator/info
    client-apikey: null
    registry-base-url: null
    collecting-cycle:
    - name: every30second
      corn-express: 0/30 * * * * ?
    rules:
    - msu-name: sys
      enabled: false
      collectors:
      - name: cpu-collector
        corn-name: every30second
        scope: INSTANCE
      - name: mem-collector
        corn-name: every30second
        scope: INSTANCE
      - name: sys-collector
        corn-name: every30second
        scope: INSTANCE
      - name: disk-collector
        corn-name: every30second
        scope: INSTANCE
cxf:
  path: /api
  servlet:
    init:
      hide-service-list-page: true
spring:
  mvc:
    servlet:
      load-on-startup: 0
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ENC(gXYqk50ecni3KhjvZL/fsSTY8cpWTP65hsyCpWnp66/B6FMYPEYk8Q+Qb6ZZvveycBkhTFocKCh7tqdUSJ0GNw==)
    driver-class-name: org.postgresql.Driver
    username: ENC(qOXUneSZxSstMRvGghH1/A4z6yNwSf3He0VscnQfhwc=)
    password: ENC(a1XaQJkZvTdOFXJ8BhgNXMPuWVMP5wv+kV6LHDbtr7ZANY4lfBft1w==)
    hikari:
      pool-name: hikari-cp
      connection-test-query: SELECT 1
      maximum-pool-size: 100
      minimum-idle: 1
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        jdbc:
          batch_size: 10
        temp:
          use_jdbc_metadata_defaults: false
        dialect: org.hibernate.dialect.CAFPostgreSQL95Dialect
        show_sql: false
        format_sql: false
        generate_statistics: false
        bytecode:
          use_reflection_optimizer: true
        multiTenancy: database
        multi_tenant_connection_provider: io.iec.edp.caf.tenancy.core.extensions.MultiTenantConnectionProviderImpl
        tenant_identifier_resolver: io.iec.edp.caf.tenancy.core.extensions.MultiTenantIdentifierResolver
        implicit_naming_stategy: io.iec.edp.caf.data.orm.jpa.CAFImplicitNamingStrategy
        physical_naming_stategy: io.iec.edp.caf.data.orm.jpa.CAFPhysicalNamingStrategy
        ejb:
          interceptor: io.iec.edp.caf.data.orm.jpa.CAFHibernateInterceptor
    open-in-view: false
  data:
    redis:
      repositories:
        enabled: true
  main:
    lazy-initialization: false
    allow-circular-references: true
  application:
    edition: Standard-File
  session:
    store-type: redis
    timeout: 1800
  redis:
    database: 0
    host: 127.0.0.1
    port: 6380
    password: ENC(zunnl8Aor1B7PVYp/1SjosBc1dJ9KDyJlf5VIhu3sdPye64deVIJXA==)
  profiles:
    active: prod
  cloud:
    stream:
      defaultBinder: rabbit
      binders:
        rabbit:
          type: rabbit
          environment:
            spring:
              rabbitmq:
                host: null
                port: 5672
                username: null
                password: null
                virtual-host: /
  banner:
    location: banner-gscloud.txt
nacos:
  discovery:
    server-addr: 127.0.0.1:8848
msu:
  enable: false
  serviceName: MicroServiceUnit
  applicationName: iGIX-Server
  deployMode: OnPremise
  ip:
    prefix: ''
server:
  port: 5200
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types:
    - text/html
    - text/xml
    - text/plain
    - text/css
    - text/javascript
    - application/javascript
    - application/json
    - application
    - image/svg+xml
    - image/png
    - image/jpeg
    min-response-size: 1024
  tomcat:
    threads:
      max: 500
    accept-count: 1000
    max-connections: 20000
    connection-timeout: 60000
jasypt:
  encryptor:
    bean: cafConfigEncryptor
    proxyPropertySources: true
redis:
  enabled: true
multi-tenant:
  mode: exclusive
parallel:
  base:
    paths:
    - platform/common
    - platform/runtime/common
    - platform/runtime/sys
    - apps/fast
    - apps/bf
    - apps/bp
  module:
    principle: keyApp
    includes:
    - apps
    - platform/runtime
    - platform/dev
    special-modules:
    - name: mdmsoeo
      includes:
      - apps/mdm
      - apps/sasa
      - apps/tiol
    - name: tmhb
      includes:
      - apps/tm
      - apps/ihc
    - name: pphb
      includes:
      - apps/pp
      - apps/pd
      - apps/mom
    excludes:
    - apps/bcp
    - apps/tftc
caf-error-page:
  enabled: false
  pages:
  - status: 403
    path: /platform/common/web/message/403.html
  - status: 404
    path: /platform/common/web/message/404.html
  - status: 500
    path: /platform/common/web/message/500.html
caching-configuration:
  enableRedis: true
  redisManagers:
  - name: default
    mode: standalone
    host: 127.0.0.1
    port: 6380
    password: ENC(ySt/U+y7G6OG6H+ArC204f9wGagCbe1RsRKlaNcNstZjbaYPLEf2Fw==)
logging:
  config: ${spring.config.location}/logback-spring.xml
  level:
    root: error
    org:
      springframework:
        amqp:
          rabbit:
            connection:
              CachingConnectionFactory: error
            listener:
              SimpleMessageListenerContainer: error
    io:
      seata:
        core:
          rpc:
            netty:
              NettyClientChannelManager: error
              AbstractRpcRemoting: error
              NettyPoolableFactory: error
              RmRpcClient: error
        common:
          exception:
            FrameworkException: error
      iec:
        edp:
          caf:
            commons:
              configclient:
                ConfigServicePropertySourceLocator: info
    i:
      s:
        c:
          r:
            netty:
              NettyClientChannelManager: fatal
    com:
      inspur:
        gs:
          bf:
            df:
              externaltools:
                core:
                  service:
                    BFUtils: error
  collection:
    enabled: false
    host: 127.0.0.1
    port: 8084
    clientKey: iGIX
message-settings:
  defaultChannel: default
  channels:
  - channelName: default
    senderTypes: sms,email,cloudplus
    msgSendParamInfos:
    - sendType: email
      params:
        server: null
        port: null
        account: null
        password: null
  userMappers:
  - name: default
    type: com.inspur.edp.svc.message.extension.mapper.GspUserMapper
  msgSenders:
  - name: cloudplus
    type: com.inspur.edp.svc.message.extension.cloudplus.CloudPlusSender
  - name: email
    type: com.inspur.edp.svc.message.extension.email.EmailMsgSender
  - name: sms
    type: com.inspur.edp.svc.message.extension.sms.SmsMsgSender
  msgContentBuilders:
  - name: cloudplus
    type: com.inspur.edp.svc.message.extension.builder.CloudPlusBuilder
  - name: email
    type: com.inspur.edp.svc.message.extension.builder.EmailContentBuilder
  - name: sms
    type: com.inspur.edp.svc.message.extension.builder.SmsContentBuilder
ebs:
  stream:
    enabled: false
rri:
  enabled: false
  black-list:
  - io.iec.edp.caf.audit.api.data.GspAudit
  - io.iec.edp.caf.scheduler.api.data.GspScheduleLog
  - com.inspur.edp.rri.common.entity.RriDataSubscription
  - com.inspur.edp.rri.common.entity.RriDataService
  - com.inspur.edp.rri.common.entity.RriDataProcessor
  - com.inspur.edp.rri.common.entity.RriDataPublisher
  - com.inspur.edp.rri.common.entity.RriDataConsumer
  - com.inspur.edp.rri.common.entity.RriHandlerLog
  - com.inspur.edp.rri.common.entity.RriDataRelation
  - com.inspur.edp.rri.center.entity.parameter.RriParamConfig
  - com.inspur.edp.rri.common.entity.RriPublishLog
  - com.inspur.edp.rri.common.entity.RriPublishData
  - com.inspur.edp.rri.common.entity.RriStrategyConfig
  - com.inspur.edp.rri.common.entity.RriConfig
  - com.inspur.edp.event.stream.center.api.entity.BizEvent
  - com.inspur.edp.event.stream.center.api.entity.EventSubscriber
  - com.inspur.edp.event.stream.center.api.entity.EventPubSub
  - com.inspur.edp.event.stream.center.api.entity.EventProcessor
  - com.inspur.edp.event.stream.center.api.entity.EventConsumer
  - com.inspur.edp.event.stream.center.api.entity.EventPublisher
  - com.inspur.edp.event.stream.entity.eventlog.EventLog
  - com.inspur.edp.event.stream.entity.eventlog.EventHandleLog
  - com.inspur.edp.stream.center.api.MessageChannel
  - com.inspur.edp.event.stream.center.routing.EventDimension
  - com.inspur.edp.event.stream.center.routing.RoutingRecord
  - com.inspur.edp.event.stream.center.routing.EventRouteGroup
  - com.inspur.edp.event.stream.entity.paramconfig.EventParamConfig
  - io.iec.edp.caf.permission.api.data.designtime.Permission.UserOpCache
dtx:
  enabled: false
  registry:
    type: file
  config:
    type: file
  service:
    vgroupMapping:
      GSCloud-seata-service-group: default
    grouplist:
      default: 127.0.0.1:8091
datasource-auto-proxy:
  enabled: true
preload:
  enable: true
  suList:
  - su: pfcommon
    packages: com.inspur.edp
  - su: arapm
    packages: com.inspur.gs.arap
  - su: ctm
    packages: com.inspur.gs.ct.ctm
  - su: ctp
    packages: com.inspur.gs.ct.ctp
  - su: ctb
    packages: com.inspur.gs.ct.ctb
  - su: ctc
    packages: com.inspur.gs.ct.ctc
  - su: cts
    packages: com.inspur.gs.ct.cts
  - su: df
    packages: com.inspur.gs.bf
qa:
  offline:
    delanswer: true
fastdweb:
  dispatch: false
  appsu: null
startup:
  messages:
  - The GSCloud server has started successfully in {uptime} seconds，Now listening
    on:{port}
msu-config:
  strategy: blackList
